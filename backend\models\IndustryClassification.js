const { getDatabase } = require('../config/database');

class IndustryClassification {
  // 分页获取国民经济行业分类
  static getPaginated(page = 1, pageSize = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const offset = (page - 1) * pageSize;
      
      let whereClause = 'WHERE 1=1';
      let params = [];
      
      // 搜索条件
      if (filters.search) {
        whereClause += ' AND (primary_name LIKE ? OR code LIKE ?)';
        params.push(`%${filters.search}%`, `%${filters.search}%`);
      }
      
      if (filters.level) {
        whereClause += ' AND level = ?';
        params.push(filters.level);
      }
      
      if (filters.parent_code) {
        whereClause += ' AND parent_code = ?';
        params.push(filters.parent_code);
      }
      
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM industry_classification ${whereClause}`;
      
      db.get(countSql, params, (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 获取分页数据
        const dataSql = `
          SELECT * FROM industry_classification 
          ${whereClause}
          ORDER BY code
          LIMIT ? OFFSET ?
        `;
        const dataParams = [...params, pageSize, offset];
        
        db.all(dataSql, dataParams, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total: countResult.total,
              page,
              pageSize,
              totalPages: Math.ceil(countResult.total / pageSize)
            });
          }
        });
      });
    });
  }

  // 获取树形结构数据
  static getTreeData(parentCode = null) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      let sql, params;
      if (parentCode) {
        sql = `SELECT * FROM industry_classification WHERE parent_code = ? ORDER BY code`;
        params = [parentCode];
      } else {
        sql = `SELECT * FROM industry_classification WHERE level = 1 ORDER BY code`;
        params = [];
      }
      
      db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据ID获取行业分类
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.get("SELECT * FROM industry_classification WHERE id = ?", [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 根据代码获取行业分类
  static getByCode(code) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.get("SELECT * FROM industry_classification WHERE code = ?", [code], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建行业分类
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const {
        code, level, level_name, primary_name, detailed_description, 
        full_path_description, parent_code
      } = data;
      
      db.run(
        `INSERT INTO industry_classification (
          code, level, level_name, primary_name, detailed_description,
          full_path_description, parent_code
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [code, level, level_name, primary_name, detailed_description, full_path_description, parent_code],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, ...data });
          }
        }
      );
    });
  }

  // 更新行业分类
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const {
        code, level, level_name, primary_name, detailed_description,
        full_path_description, parent_code
      } = data;
      
      db.run(
        `UPDATE industry_classification SET 
          code = ?, level = ?, level_name = ?, primary_name = ?, 
          detailed_description = ?, full_path_description = ?, parent_code = ?
         WHERE id = ?`,
        [code, level, level_name, primary_name, detailed_description, full_path_description, parent_code, id],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id, ...data });
          }
        }
      );
    });
  }

  // 删除行业分类
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM industry_classification WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }

  // 搜索行业分类
  static search(keyword) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      if (!keyword) {
        reject(new Error('搜索关键词不能为空'));
        return;
      }

      // 判断是代码还是文本
      const isCode = /^\d+$/.test(keyword);
      
      let query, params;
      
      if (isCode) {
        // 按代码精准匹配
        query = `
          SELECT code, level, level_name, primary_name, detailed_description, full_path_description
          FROM industry_classification 
          WHERE code = ?
        `;
        params = [keyword];
      } else {
        // 按名称精准匹配
        query = `
          SELECT code, level, level_name, primary_name, detailed_description, full_path_description
          FROM industry_classification 
          WHERE primary_name = ?
        `;
        params = [keyword];
      }

      db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 获取详细信息（包含子分类）
  static getDetail(name) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      if (!name) {
        reject(new Error('行业名称不能为空'));
        return;
      }

      // 1. 首先检查是否是 industries 表中的具体项目
      db.get('SELECT * FROM industries WHERE name = ?', [name], (err, industry) => {
        if (err) {
          reject(err);
          return;
        }

        if (industry) {
          // 如果是具体的环评项目，通过分类信息查找对应的国民经济行业分类
          this.findClassificationByIndustry(industry, resolve, reject);
        } else {
          // 如果不是具体项目，按原来的逻辑在 industry_classification 表中查找
          this.findClassificationByName(name, resolve, reject);
        }
      });
    });
  }

  // 通过具体行业项目查找对应的国民经济行业分类
  static findClassificationByIndustry(industry, resolve, reject) {
    const db = getDatabase();

    // 根据行业分类信息智能匹配
    let searchTerms = [];

    // 从 category 字段提取关键词
    if (industry.category) {
      const category = industry.category;
      if (category.includes('农业') || category.includes('林业')) {
        searchTerms.push('农业', '种植', '林业');
      } else if (category.includes('畜牧业')) {
        searchTerms.push('畜牧业', '饲养');
      } else if (category.includes('渔业')) {
        searchTerms.push('渔业', '养殖');
      } else if (category.includes('煤炭')) {
        searchTerms.push('煤炭', '开采');
      } else if (category.includes('制造业')) {
        searchTerms.push('制造业');
      }
      // 可以继续添加更多的映射规则
    }

    // 从项目名称提取关键词
    const name = industry.name;
    if (name.includes('农产品') || name.includes('药材')) {
      searchTerms.push('农业', '种植', '中药材');
    } else if (name.includes('林基地')) {
      searchTerms.push('林业');
    } else if (name.includes('饲养')) {
      searchTerms.push('畜牧业', '饲养');
    } else if (name.includes('养殖')) {
      searchTerms.push('渔业', '养殖');
    }

    if (searchTerms.length === 0) {
      // 如果没有找到合适的搜索词，返回一个通用的结果
      resolve({
        main: {
          code: 'UNKNOWN',
          primary_name: industry.name,
          level: 0,
          detailed_description: `具体项目：${industry.name}`,
          full_path_description: industry.category || '未分类'
        },
        descendants: []
      });
      return;
    }

    // 构建搜索查询
    const conditions = searchTerms.map(() => 'primary_name LIKE ?').join(' OR ');
    const params = searchTerms.map(term => `%${term}%`);

    const query = `
      SELECT * FROM industry_classification
      WHERE ${conditions}
      ORDER BY level ASC, length(primary_name) ASC
      LIMIT 1
    `;

    db.get(query, params, (err, mainClassification) => {
      if (err) {
        reject(err);
        return;
      }

      if (!mainClassification) {
        // 如果还是找不到，返回一个基于行业信息的结果
        resolve({
          main: {
            code: 'INDUSTRY_' + industry.id,
            primary_name: industry.name,
            level: 0,
            detailed_description: `环评项目：${industry.name}`,
            full_path_description: industry.category || '未分类'
          },
          descendants: []
        });
        return;
      }

      // 查找子分类
      const descendantsQuery = `
        WITH RECURSIVE classification_tree AS (
          SELECT * FROM industry_classification WHERE code = ?
          UNION ALL
          SELECT t.*
          FROM industry_classification t
          JOIN classification_tree ct ON t.parent_code = ct.code
        )
        SELECT * FROM classification_tree WHERE code != ?;
      `;

      db.all(descendantsQuery, [mainClassification.code, mainClassification.code], (err, descendants) => {
        if (err) {
          reject(err);
        } else {
          resolve({
            main: mainClassification,
            descendants: descendants || []
          });
        }
      });
    });
  }

  // 按名称在 industry_classification 表中查找
  static findClassificationByName(name, resolve, reject) {
    const db = getDatabase();

    // 智能清理名称：移除末尾的注释括号和代码
    const pureName = name.replace(/（[^）]+）\s*\d*$/, '').replace(/[\d\*]+$/, '').trim();

    // 查找最匹配的主分类
    const findMainQuery = `
      SELECT * FROM industry_classification
      WHERE primary_name LIKE ?
      ORDER BY level ASC, length(primary_name) ASC
      LIMIT 1
    `;

    db.get(findMainQuery, [`%${pureName}%`], (err, mainClassification) => {
      if (err) {
        reject(err);
        return;
      }
      if (!mainClassification) {
        reject(new Error(`未找到与"${pureName}"相关的行业`));
        return;
      }

      // 查找子分类
      const descendantsQuery = `
        WITH RECURSIVE classification_tree AS (
          SELECT * FROM industry_classification WHERE code = ?
          UNION ALL
          SELECT t.*
          FROM industry_classification t
          JOIN classification_tree ct ON t.parent_code = ct.code
        )
        SELECT * FROM classification_tree WHERE code != ?;
      `;

      db.all(descendantsQuery, [mainClassification.code, mainClassification.code], (err, descendants) => {
        if (err) {
          reject(err);
        } else {
          resolve({
            main: mainClassification,
            descendants: descendants || []
          });
        }
      });
    });
  }

  // 获取级联选择器格式的国民经济行业分类数据
  static getCascadeData() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      // 获取所有行业分类数据
      const query = `
        SELECT id, code, primary_name, level, parent_code
        FROM industry_classification
        ORDER BY code
      `;

      db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        // 构建层级结构
        const buildTree = (parentCode = null, level = 1) => {
          return rows
            .filter(row => {
              if (level === 1) {
                return row.level === 1; // 第一级：A, B, C, D, E...
              } else {
                return row.parent_code === parentCode;
              }
            })
            .map(row => {
              const children = buildTree(row.code, level + 1);
              return {
                id: row.id,
                name: row.primary_name,
                code: row.code,
                level: row.level,
                children: children.length > 0 ? children : undefined
              };
            });
        };

        const result = buildTree();
        resolve(result);
      });
    });
  }

  // 获取行业分类信息（包含层级路径）
  static getClassificationInfo(industryId) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      // 根据ID获取行业分类信息
      const query = `
        SELECT id, code, primary_name, level, parent_code
        FROM industry_classification
        WHERE id = ?
      `;

      db.get(query, [industryId], (err, classification) => {
        if (err) {
          reject(err);
          return;
        }

        if (!classification) {
          resolve(null);
          return;
        }

        // 获取完整的层级路径
        this.getClassificationPath(classification.code)
          .then(path => {
            resolve({
              ...classification,
              path: path
            });
          })
          .catch(reject);
      });
    });
  }

  // 获取分类的完整层级路径
  static getClassificationPath(code) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      // 使用递归CTE获取从根到当前节点的路径
      const query = `
        WITH RECURSIVE classification_path AS (
          SELECT id, code, primary_name, level, parent_code, 0 as depth
          FROM industry_classification
          WHERE code = ?

          UNION ALL

          SELECT p.id, p.code, p.primary_name, p.level, p.parent_code, cp.depth + 1
          FROM industry_classification p
          JOIN classification_path cp ON p.code = cp.parent_code
        )
        SELECT * FROM classification_path ORDER BY depth DESC
      `;

      db.all(query, [code], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}

module.exports = IndustryClassification;
