const { initDatabase, getDatabase } = require('./config/database');

async function checkDatabase() {
  await initDatabase();
  const db = getDatabase();

  console.log('=== 检查数据库状态 ===\n');

  // 1. 检查表结构
  console.log('1. 检查行业相关表：');
  db.all("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%industry%'", (err, tables) => {
    if (err) {
      console.error('查询表失败:', err);
      return;
    }
    
    tables.forEach(table => {
      console.log(`  - ${table.name}`);
    });

    // 2. 检查 industry_focus_point_relations 表结构
    console.log('\n2. 检查 industry_focus_point_relations 表结构：');
    db.all("PRAGMA table_info(industry_focus_point_relations)", (err, columns) => {
      if (err) {
        console.error('查询表结构失败:', err);
        return;
      }
      
      columns.forEach(col => {
        console.log(`  - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.pk ? 'PRIMARY KEY' : ''}`);
      });

      // 3. 检查关联数据
      console.log('\n3. 检查行业关注点关联数据：');
      db.all(`
        SELECT 
          r.id,
          r.industry_focus_point_id,
          r.industry_id,
          ifp.name as focus_point_name,
          i.name as industry_name
        FROM industry_focus_point_relations r
        LEFT JOIN industry_focus_points ifp ON r.industry_focus_point_id = ifp.id
        LEFT JOIN industries i ON r.industry_id = i.id
        LIMIT 10
      `, (err, relations) => {
        if (err) {
          console.error('查询关联数据失败:', err);
          return;
        }

        if (relations.length === 0) {
          console.log('  没有找到关联数据');
        } else {
          console.log(`  找到 ${relations.length} 条关联数据：`);
          relations.forEach(rel => {
            console.log(`    - 关注点: ${rel.focus_point_name} → 行业: ${rel.industry_name || '未找到'} (ID: ${rel.industry_id})`);
          });
        }

        // 4. 检查行业关注点总数
        console.log('\n4. 检查行业关注点总数：');
        db.get("SELECT COUNT(*) as count FROM industry_focus_points", (err, result) => {
          if (err) {
            console.error('查询关注点总数失败:', err);
            return;
          }
          console.log(`  行业关注点总数: ${result.count}`);

          // 5. 检查分类管理名录总数
          console.log('\n5. 检查分类管理名录总数：');
          db.get("SELECT COUNT(*) as count FROM industries", (err, result) => {
            if (err) {
              console.error('查询行业总数失败:', err);
              return;
            }
            console.log(`  分类管理名录总数: ${result.count}`);

            console.log('\n=== 检查完成 ===');
            process.exit(0);
          });
        });
      });
    });
  });
}

checkDatabase().catch(console.error);
