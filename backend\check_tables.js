const { initDatabase, getDatabase } = require('./config/database');

async function checkTables() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 检查数据库表结构 ===');
    
    // 1. 查看所有表
    console.log('\n--- 所有表 ---');
    const tables = await new Promise((resolve, reject) => {
      db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    tables.forEach(table => {
      console.log(`表名: ${table.name}`);
    });
    
    // 2. 查看industries表结构
    console.log('\n--- industries表结构 ---');
    const industriesSchema = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(industries)", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    industriesSchema.forEach(col => {
      console.log(`字段: ${col.name}, 类型: ${col.type}, 非空: ${col.notnull}, 默认值: ${col.dflt_value}`);
    });
    
    // 3. 检查是否有industry_classifications表
    const hasClassifications = tables.find(t => t.name === 'industry_classifications');
    if (hasClassifications) {
      console.log('\n--- industry_classifications表结构 ---');
      const classificationsSchema = await new Promise((resolve, reject) => {
        db.all("PRAGMA table_info(industry_classifications)", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });
      
      classificationsSchema.forEach(col => {
        console.log(`字段: ${col.name}, 类型: ${col.type}, 非空: ${col.notnull}, 默认值: ${col.dflt_value}`);
      });
    } else {
      console.log('\n--- industry_classifications表不存在 ---');
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('检查失败:', error);
    process.exit(1);
  }
}

checkTables();
