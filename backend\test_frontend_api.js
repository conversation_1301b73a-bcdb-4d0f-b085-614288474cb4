const http = require('http');
const { URL } = require('url');

async function testFrontendApi() {
  try {
    console.log('=== 测试前端API ===');

    // 测试必选项查询API
    const baseUrl = 'http://localhost:3001/api/search/required';
    const params = new URLSearchParams({
      region_id: 1,
      in_park: true,
      park_id: 2,
      industry_id: 1
    });

    const url = `${baseUrl}?${params}`;
    console.log('请求URL:', url);

    const response = await new Promise((resolve, reject) => {
      const req = http.get(url, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve({
              status: res.statusCode,
              data: JSON.parse(data)
            });
          } catch (e) {
            reject(e);
          }
        });
      });
      req.on('error', reject);
    });

    console.log('\n=== API响应 ===');
    console.log('状态码:', response.status);
    console.log('响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // 检查园区要求数据
    if (response.data.data && response.data.data.park_policies) {
      console.log('\n=== 园区要求详情 ===');
      response.data.data.park_policies.forEach((policy, index) => {
        console.log(`${index + 1}. ${policy.requirement_title}`);
        console.log(`   分类: ${policy.category || '未设置'}`);
        console.log(`   类型: ${policy.requirement_type || '未设置'}`);
        console.log(`   内容: ${policy.requirement_content.substring(0, 50)}...`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testFrontendApi();
