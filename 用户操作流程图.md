# 蓝天Wiki 用户操作流程图（修正版）

## 筛选逻辑重新设计

### 筛选项结构：
```
必选项（必须关注的分类）：
├── 地区选择
├── 是否在园区  
├── 园区名称/三线一单管控单元
└── 行业分类

可选项（可能需要关注的部分）：
├── 行业关注关键词
└── 通用关注关键词
```

## 主要筛选流程

### 1. 用户进入主页面
```
┌─────────────────────────────────────────────────────────┐
│                    蓝天Wiki 主页面                       │
├─────────────────────────────────────────────────────────┤
│  【必选项 - 必须关注的分类】                             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐        │
│  │ 地区    │ │是否在园区│ │园区/单元 │ │行业     │        │
│  │ [请选择▼]│ │ [请选择▼]│ │ [请选择▼]│ │ [请选择▼]│        │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘        │
├─────────────────────────────────────────────────────────┤
│  【可选项 - 可能需要关注的部分】                         │
│  ┌─────────────────────┐ ┌─────────────────────┐        │
│  │ 行业关注关键词       │ │ 通用关注关键词       │        │
│  │ [可选▼]             │ │ [可选▼]             │        │
│  └─────────────────────┘ └─────────────────────┘        │
├─────────────────────────────────────────────────────────┤
│                    查询结果                              │
│  【必须关注】                                            │
│  (基于必选项的查询结果)                                   │
│  【可能需要关注】                                        │
│  (基于可选项的补充查询结果)                               │
└─────────────────────────────────────────────────────────┘
```

### 2. 地区选择（第一步 - 必选）
**地区选项：**
- 红旗区
- 卫滨区  
- 牧野区
- 凤泉区
- 新乡县
- 获嘉县
- 原阳县
- 延津县
- 封丘县
- 卫辉市
- 辉县市
- 长垣市
- **经开区**（特殊区域，包含新乡工业产业集聚区、新乡高新技术产业开发区、新乡经济技术开发区）
- **高新区**（即新乡高新技术产业开发区）

```
用户操作：选择地区 = "经开区"
↓
系统响应：
下一步"是否在园区"选项激活
```

### 3. 是否在园区（第二步 - 必选）
```
用户操作：选择"是否在园区" = "在园区"
↓
系统响应：
"园区/单元"下拉框显示该地区的园区：
- 如果选择了"经开区"：
  - 新乡工业产业集聚区
  - 新乡高新技术产业开发区  
  - 新乡经济技术开发区
- 如果选择了"高新区"：
  - 新乡高新技术产业开发区
- 如果选择了"红旗区"：
  - 新乡红旗区先进制造业开发区
  - 新乡工业产业集聚区
  - 新乡高新技术产业开发区
  - 新乡经济技术开发区
```

```
用户操作：选择"是否在园区" = "不在园区"
↓
系统响应：
"园区/单元"下拉框显示该地区的三线一单管控单元：
- 如果选择了"红旗区"：
  - 红旗区城镇重点单元
  - 红旗区一般生态空间
  - 红旗区优先保护单元
  - ...
```

### 4. 园区名称/三线一单管控单元（第三步 - 必选）
```
用户操作：选择园区 = "新乡经济技术开发区"
↓
系统响应：
"行业"下拉框激活，显示行业分类
```

### 5. 行业分类（第四步 - 必选）
**行业分类结构：**
```
制造业
├── 化工
│   ├── 电镀
│   ├── 涂装
│   └── 化学原料制造
├── 装备制造
│   ├── 机械制造
│   └── 汽车制造
├── 纺织服装
└── 食品加工

服务业
├── 物流仓储
├── 商贸服务
└── 信息技术

其他
├── 基础设施
└── 公共服务
```

```
用户操作：选择行业 = "制造业 > 化工 > 电镀"
↓
系统响应：
1. 必选项筛选完成，显示"必须关注"的查询结果
2. 可选项区域激活
```

### 6. 行业关注关键词（可选项）
```
基于选择的行业"电镀"，系统预设关键词：
- VOCs排放
- 重金属污染
- 废水处理
- 电镀槽
- 表面处理
- 清洁生产

用户可以选择其中几个关键词进行补充筛选
```

### 7. 通用关注关键词（可选项）
```
用户可自定义的标签选项：
- 环评
- 节能评估
- 安全评价
- 职业卫生
- 消防审批
- 规划许可
- 土地审批
- ...

用户可以添加自定义标签或选择已有标签
```

## 完整示例：经开区新乡经济技术开发区电镀项目查询

### 用户操作序列：
```
【必选项】
1. 地区：经开区
2. 是否在园区：在园区
3. 园区：新乡经济技术开发区
4. 行业：制造业 > 化工 > 电镀

【可选项】
5. 行业关注关键词：VOCs排放、重金属污染
6. 通用关注关键词：环评、节能评估
```

### 系统返回结果：
```
┌─────────────────────────────────────────────────────────┐
│ 查询结果                                                 │
├─────────────────────────────────────────────────────────┤
│ 🔴 【必须关注】（基于必选项的精准匹配）                   │
│                                                         │
│ 📋 三线一单管控要求                                      │
│ ├─ 新乡经济技术开发区管控单元 (ZH41070220007)            │
│ │  ├─ 空间布局约束：园区规划主导产业为...                 │
│ │  ├─ 污染物排放管控：严格控制VOCs排放...                 │
│ │  └─ 环境风险防控：重金属污染防控要求...                 │
│                                                         │
│ 📜 行业审批原则                                          │
│ ├─ 电镀建设项目环境影响评价文件审查审批原则               │
│ │  └─ 适用于新乡经济技术开发区的电镀项目审批...           │
│                                                         │
│ 🏭 园区准入要求                                          │
│ ├─ 新乡经济技术开发区产业准入负面清单                    │
│ │  └─ 电镀行业：限制类，需满足清洁生产要求...             │
├─────────────────────────────────────────────────────────┤
│ 🟡 【可能需要关注】（基于可选项的关键词匹配）             │
│                                                         │
│ 📄 相关政策文件                                          │
│ ├─ 河南省VOCs排放标准                                    │
│ │  └─ 包含"VOCs排放"关键词...                            │
│ ├─ 重金属污染防治技术政策                                │
│ │  └─ 包含"重金属污染"关键词...                          │
│ ├─ 建设项目环境影响评价分类管理名录                      │
│ │  └─ 包含"环评"关键词...                                │
│ └─ 固定资产投资项目节能审查办法                          │
│     └─ 包含"节能评估"关键词...                           │
└─────────────────────────────────────────────────────────┘
```

## 数据库结构调整

### 新增特殊区域处理
```sql
-- regions表需要标记特殊区域
ALTER TABLE regions ADD COLUMN region_type VARCHAR(20) DEFAULT 'normal';
-- 'normal': 普通行政区域
-- 'special': 特殊区域（经开区、高新区）

-- 新增区域园区关联表（处理跨区域园区）
CREATE TABLE region_park_relations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  region_id INTEGER,
  park_id INTEGER,
  is_primary BOOLEAN DEFAULT FALSE, -- 是否为主要归属
  FOREIGN KEY (region_id) REFERENCES regions(id),
  FOREIGN KEY (park_id) REFERENCES parks(id)
);
```

### 关键词标签系统
```sql
-- 行业关键词表
CREATE TABLE industry_keywords (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  industry_id INTEGER,
  keyword VARCHAR(100),
  FOREIGN KEY (industry_id) REFERENCES industries(id)
);

-- 通用标签表
CREATE TABLE general_tags (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  tag_name VARCHAR(100),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 政策标签关联表
CREATE TABLE policy_tags (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  policy_id INTEGER,
  tag_id INTEGER,
  FOREIGN KEY (policy_id) REFERENCES policies(id),
  FOREIGN KEY (tag_id) REFERENCES general_tags(id)
);
```

这样的设计实现了：
1. **正确的筛选逻辑** - 地区→是否在园区→园区/单元→行业的递进筛选
2. **特殊区域处理** - 经开区和高新区的跨区域特性
3. **必选与可选分离** - 精准匹配和模糊匹配的结果分开显示
4. **灵活的关键词系统** - 支持行业关键词和自定义标签 