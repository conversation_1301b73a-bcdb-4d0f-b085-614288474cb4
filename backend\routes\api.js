const express = require('express');
const router = express.Router();
const ApiController = require('../controllers/apiController');

// 获取区域列表
router.get('/regions', ApiController.getRegions);

// 获取园区列表（根据地区筛选）
router.get('/parks', ApiController.getParks);

// 获取单个园区信息
router.get('/parks/:id', ApiController.getParkById);

// 获取管控单元列表（根据地区筛选，排除园区）
router.get('/control-units', ApiController.getControlUnits);

// 获取行业列表（基于分类管理名录）
router.get('/industries', ApiController.getIndustries);

// 获取特定行业ID的环境影响评价要求
router.get('/industries/:industryId/requirements', ApiController.getIndustryRequirements);

// 新的、统一的国民经济行业分类详情接口
router.get('/industry-classification/detail', ApiController.getIndustryClassificationDetail);

// 搜索国民经济行业分类
router.get('/industry-classification/search', ApiController.searchIndustryClassification);

// 获取行业关键词
router.get('/industry-keywords', ApiController.getIndustryKeywords);

// 获取通用标签
router.get('/general-tags', ApiController.getGeneralTags);

// 根据行业ID获取相关的行业关注点（支持层级匹配）
router.get('/industry-focus-points/by-industry', ApiController.getIndustryFocusPointsByIndustry);

// 必选项查询（精准匹配）
router.get('/search/required', ApiController.searchRequired);

// 可选项查询（关键词匹配）
router.get('/search/optional', ApiController.searchOptional);

// 三线一单查询
router.post('/sanxianyidan', ApiController.searchSanxianyidan);

module.exports = router;
