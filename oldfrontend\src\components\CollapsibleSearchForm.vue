<template>
  <div class="search-container" :class="{ 'is-collapsed': isCollapsed }">
    <div v-if="!isCollapsed" class="search-form-card">
      <div class="card-header">
        <h2 class="card-title">请选择项目的基本信息</h2>
        <p class="card-subtitle">我们会根据您的选择，智能匹配相关的环保政策要求</p>
      </div>

      <!-- 必选项 -->
      <el-divider content-position="left">
        <span class="divider-text color-primary"><el-icon><Location /></el-icon> 基础选项 (必填)</span>
      </el-divider>
      <div class="form-grid">
        <el-form-item>
           <template #label>
            <span class="form-item-label"><el-icon class="label-icon-primary"><MapLocation /></el-icon> 项目所在地</span>
          </template>
          <el-select v-if="!isMobile" v-model="formData.regionId" placeholder="请选择地区" @change="onRegionChange" size="large">
            <el-option v-for="region in regions" :key="region.id" :label="region.name" :value="region.id" />
          </el-select>
          <el-button v-else @click="openDrawer('regionId')" size="large" class="full-width industry-select-btn">
             {{ selectedRegionName || '请选择地区' }}
          </el-button>
        </el-form-item>

        <el-form-item>
           <template #label>
            <span class="form-item-label"><el-icon class="label-icon-primary"><OfficeBuilding /></el-icon> 是否在园区</span>
          </template>
          <el-select v-if="!isMobile" v-model="formData.inPark" placeholder="请选择" :disabled="!formData.regionId" @change="onInParkChange" size="large">
            <el-option label="是" :value="true" /><el-option label="否" :value="false" />
          </el-select>
           <el-button v-else @click="openDrawer('inPark')" :disabled="!formData.regionId" size="large" class="full-width industry-select-btn">
            {{ selectedInParkText }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <template #label>
            <span class="form-item-label">
              <el-icon class="label-icon-primary"><School /></el-icon> {{ formData.inPark ? '所属园区' : '所属单元' }}
            </span>
          </template>
           <el-select v-if="!isMobile" v-model="formData.parkOrUnitId" :placeholder="formData.inPark ? '请选择园区' : '请选择管控单元'" :disabled="formData.inPark === null" filterable size="large">
            <el-option v-for="item in parkOrUnitOptions" :key="item.id" :label="item.name || item.unit_name" :value="item.id" />
          </el-select>
          <el-button v-else @click="openDrawer('parkOrUnitId')" :disabled="formData.inPark === null" size="large" class="full-width industry-select-btn">
            {{ selectedParkOrUnitName || (formData.inPark ? '请选择园区' : '请选择管控单元') }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <template #label>
            <span class="form-item-label"><el-icon class="label-icon-primary"><Files /></el-icon> 所属行业</span>
          </template>
          <!-- Desktop: Cascader -->
          <el-cascader
            v-if="!isMobile"
            v-model="formData.industryId"
            :options="industryOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            placeholder="请选择行业"
            :disabled="!formData.parkOrUnitId"
            @change="onIndustryChange"
            filterable
            size="large"
            class="full-width"
          />
          <!-- Mobile: Button + Drawer -->
          <el-button 
            v-else 
            @click="industryDrawerVisible = true" 
            :disabled="!formData.parkOrUnitId"
            size="large" 
            class="full-width industry-select-btn"
          >
            {{ selectedIndustryName || '请选择行业' }}
          </el-button>
        </el-form-item>
      </div>
      
      <hr class="form-divider" />

      <!-- 可选项 -->
      <el-divider content-position="left">
        <span class="divider-text color-optional"><el-icon><CollectionTag /></el-icon> 关注点 (选填)</span>
      </el-divider>
      <div class="form-grid">
        <el-form-item>
          <template #label>
            <span class="form-item-label"><el-icon class="label-icon-optional"><PriceTag /></el-icon> 行业关注点</span>
          </template>
          <el-select v-if="!isMobile" v-model="formData.industryKeywords" placeholder="可多选" multiple @change="onOptionalChange" size="large" collapse-tags collapse-tags-tooltip>
            <el-option v-for="keyword in industryKeywords" :key="keyword" :label="keyword" :value="keyword" />
          </el-select>
           <el-button v-else @click="openDrawer('industryKeywords')" size="large" class="full-width industry-select-btn">
            {{ selectedKeywordsText }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <template #label>
            <span class="form-item-label"><el-icon class="label-icon-optional"><Tickets /></el-icon> 通用关注点</span>
          </template>
          <el-select v-if="!isMobile" v-model="formData.generalTags" placeholder="可多选" multiple @change="onOptionalChange" size="large" collapse-tags collapse-tags-tooltip>
            <el-option v-for="tag in generalTags" :key="tag.id" :label="tag.tag_name" :value="tag.id" />
          </el-select>
           <el-button v-else @click="openDrawer('generalTags')" size="large" class="full-width industry-select-btn">
             {{ selectedTagsText }}
          </el-button>
        </el-form-item>
      </div>

      <div class="form-actions">
        <el-button size="large" @click="handleReset" class="reset-btn">
          <el-icon><RefreshRight /></el-icon> 重置
        </el-button>
        <el-button type="primary" size="large" :disabled="!canSearch" @click="handleSearch" :loading="searching" class="search-btn">
          开始查询 <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    
    <div v-else class="collapsed-summary-card" @click="expandForm">
      <div class="summary-item">
        <strong>行业：</strong>
        <span>{{ summary.industry || '未选择' }}</span>
      </div>
      <div class="summary-item">
        <strong>地区：</strong>
        <span>{{ summary.region || '未选择' }}</span>
      </div>
      <div class="summary-item">
        <strong>园区/单元：</strong>
        <span>{{ summary.parkOrUnit || '未选择' }}</span>
      </div>
      <el-button type="primary" plain>
        <el-icon><Edit /></el-icon>
        修改筛选
      </el-button>
    </div>

    <!-- Mobile Industry Selection Drawer (Hierarchical) -->
    <el-drawer
        v-model="industryDrawerVisible"
        :title="mobileNavState.title"
        direction="btt"
        size="90%"
        @open="resetMobileSearch"
        class="industry-drawer"
    >
        <template #header="{ close, titleId, titleClass }">
            <h4 :id="titleId" :class="titleClass">{{ mobileNavState.title }}</h4>
            <el-button v-if="mobileNavState.path.length > 0" @click="goUpMobileNav" text>返回上一级</el-button>
        </template>
        
        <div class="drawer-search-box">
             <el-input
                v-model="mobileSearchKeyword"
                placeholder="搜索当前层级的行业..."
                clearable
                size="large"
             >
                <template #prefix>
                    <el-icon><Search /></el-icon>
                </template>
            </el-input>
        </div>

        <div class="drawer-content">
            <div 
                v-for="option in filteredMobileOptions" 
                :key="option.id" 
                class="drawer-option"
                @click="handleMobileOptionClick(option)"
            >
                <span>{{ option.name }}</span>
                <el-icon v-if="option.children && option.children.length > 0"><ArrowRight /></el-icon>
            </div>
            <div v-if="filteredMobileOptions.length === 0" class="drawer-empty">
                无匹配结果
            </div>
        </div>
    </el-drawer>
    
    <!-- Mobile Generic Selection Drawer (Flat List) -->
    <el-drawer
        v-model="genericDrawer.visible"
        :title="genericDrawer.title"
        direction="btt"
        size="80%"
        class="generic-drawer"
    >
        <div class="drawer-content">
            <div 
                v-for="option in genericDrawer.options" 
                :key="option.value" 
                class="drawer-option"
                :class="{ 'is-selected': isOptionSelected(option.value) }"
                @click="handleGenericOptionClick(option)"
            >
                <span>{{ option.label }}</span>
                <el-icon v-if="isOptionSelected(option.value)"><Select /></el-icon>
            </div>
             <div v-if="!genericDrawer.options || genericDrawer.options.length === 0" class="drawer-empty">
                无可用选项
            </div>
        </div>
        <div v-if="genericDrawer.isMulti" class="drawer-footer">
            <el-button @click="cancelMultiSelect" size="large">取消</el-button>
            <el-button type="primary" @click="confirmMultiSelect" size="large">确定</el-button>
        </div>
    </el-drawer>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';
import { apiService } from '../utils/api';
import { ElMessage, ElFormItem, ElSelect, ElOption, ElCascader, ElButton, ElIcon, ElDrawer, ElDivider } from 'element-plus';
import { RefreshRight, ArrowRight, Edit, Select, Search, Location, MapLocation, OfficeBuilding, School, Files, CollectionTag, PriceTag, Tickets } from '@element-plus/icons-vue';
import { useBreakpoint } from '../utils/useBreakpoint.js';

export default {
  name: 'CollapsibleSearchForm',
  components: { ElFormItem, ElSelect, ElOption, ElCascader, ElButton, ElIcon, ElDrawer, ElDivider, RefreshRight, ArrowRight, Edit, Select, Search, Location, MapLocation, OfficeBuilding, School, Files, CollectionTag, PriceTag, Tickets },
  props: {
    regions: Array,
    industryOptions: Array,
    generalTags: Array,
    searching: Boolean,
    isCollapsed: Boolean,
  },
  emits: ['search', 'reset', 'optional-change', 'update:is-collapsed'],
  setup(props, { emit }) {
    const { isMobile } = useBreakpoint();
    const industryDrawerVisible = ref(false);

    const formData = ref({
      regionId: null,
      inPark: null,
      parkOrUnitId: null,
      industryId: [],
      industryKeywords: [],
      generalTags: [],
    });
    
    const parkOrUnitOptions = ref([]);
    const industryKeywords = ref([]);
    const summary = ref({ region: '', parkOrUnit: '', industry: '' });

    const canSearch = computed(() => formData.value.regionId && formData.value.inPark !== null && formData.value.parkOrUnitId && formData.value.industryId.length > 0);

    const onRegionChange = () => {
      formData.value.inPark = null;
      formData.value.parkOrUnitId = null;
      formData.value.industryId = [];
      formData.value.industryKeywords = [];
      formData.value.generalTags = [];
      parkOrUnitOptions.value = [];
      industryKeywords.value = [];
    };

    const onInParkChange = async (inPark) => {
      formData.value.parkOrUnitId = null;
      formData.value.industryId = [];
      formData.value.industryKeywords = [];
      formData.value.generalTags = [];
      industryKeywords.value = [];
      if (formData.value.regionId === null) return;
      try {
        const service = inPark ? apiService.getParks : apiService.getControlUnits;
        const response = await service(formData.value.regionId);
        parkOrUnitOptions.value = response.data;
      } catch (error) {
        ElMessage.error(`加载${inPark ? '园区' : '管控单元'}失败`);
      }
    };

    // --- Mobile Drawer Navigation State ---
    const mobileSearchKeyword = ref('');
    const mobileNavState = ref({
        path: [], // array of parent nodes
        currentOptions: [],
        title: '选择行业分类'
    });

    watch(
        () => props.industryOptions,
        (newOptions) => {
            if (mobileNavState.value.path.length === 0) {
                 mobileNavState.value.currentOptions = newOptions;
            }
        },
        { immediate: true, deep: true }
    );

    const filteredMobileOptions = computed(() => {
        if (!mobileSearchKeyword.value) {
            return mobileNavState.value.currentOptions;
        }
        return mobileNavState.value.currentOptions.filter(opt => 
            opt.name.toLowerCase().includes(mobileSearchKeyword.value.toLowerCase())
        );
    });

    const resetMobileSearch = () => {
        mobileSearchKeyword.value = '';
    }

    const goUpMobileNav = () => {
        resetMobileSearch();
        mobileNavState.value.path.pop();
        if (mobileNavState.value.path.length === 0) {
            mobileNavState.value.currentOptions = props.industryOptions;
            mobileNavState.value.title = '选择行业分类';
        } else {
            const parent = mobileNavState.value.path[mobileNavState.value.path.length - 1];
            mobileNavState.value.currentOptions = parent.children;
            mobileNavState.value.title = parent.name;
        }
    };

    const handleMobileOptionClick = (option) => {
        resetMobileSearch();
        if (option.children && option.children.length > 0) {
            // Navigate deeper
            mobileNavState.value.path.push(option);
            mobileNavState.value.currentOptions = option.children;
            mobileNavState.value.title = option.name;
        } else {
            // It's a leaf node, select it
            const newIndustryId = [...mobileNavState.value.path.map(p => p.id), option.id];
            formData.value.industryId = newIndustryId;
            onIndustryChange(newIndustryId);
            industryDrawerVisible.value = false;
        }
    };
    
    const selectedIndustryName = computed(() => {
        if (!formData.value.industryId || formData.value.industryId.length === 0) return '';
        let options = props.industryOptions;
        let name = '';
        for (const id of formData.value.industryId) {
            const found = options.find(o => o.id === id);
            if (found) {
                name = found.name;
                options = found.children || [];
            } else {
                return ''; // Should not happen
            }
        }
        return name;
    });

    const onIndustryChange = async (value) => {
      formData.value.industryKeywords = [];
      if (!value || value.length === 0) {
        industryKeywords.value = [];
        return;
      }
      try {
        const industryId = value[value.length - 1];
        const response = await apiService.getIndustryKeywords(industryId);
        industryKeywords.value = response.data;
      } catch (error) {
        ElMessage.error('加载行业关键词失败');
      }
    };
    
    const onOptionalChange = () => emit('optional-change');

    const updateSummary = () => {
        const region = props.regions.find(r => r.id === formData.value.regionId);
        summary.value.region = region ? region.name : '';

        const parkOrUnit = parkOrUnitOptions.value.find(p => p.id === formData.value.parkOrUnitId);
        summary.value.parkOrUnit = parkOrUnit ? (parkOrUnit.name || parkOrUnit.unit_name) : '';
        
        if (formData.value.industryId.length > 0) {
            const industryId = formData.value.industryId[formData.value.industryId.length - 1];
            const findIndustry = (options, id) => {
                for (const option of options) {
                    if (option.id === id) return option;
                    if (option.children) {
                        const found = findIndustry(option.children, id);
                        if (found) return found;
                    }
                }
                return null;
            };
            const industry = findIndustry(props.industryOptions, industryId);
            summary.value.industry = industry ? industry.name : '';
        } else {
            summary.value.industry = '';
        }
    };

    const handleSearch = () => {
      updateSummary();
      emit('search', JSON.parse(JSON.stringify(formData.value)));
    };

    const handleReset = () => {
      formData.value = { regionId: null, inPark: null, parkOrUnitId: null, industryId: [], industryKeywords: [], generalTags: [] };
      parkOrUnitOptions.value = [];
      industryKeywords.value = [];
      summary.value = { region: '', parkOrUnit: '', industry: '' };
      emit('reset');
      resetMobileNav();
    };
    
    const expandForm = () => emit('update:is-collapsed', false);

    const getFormData = () => JSON.parse(JSON.stringify(formData.value));

    // --- Generic Drawer State & Logic ---
    const genericDrawer = ref({
        visible: false,
        title: '',
        options: [],
        targetKey: null,
        isMulti: false,
        tempMultiSelection: [],
    });

    const drawerConfig = {
        regionId: {
            title: '选择地区',
            getOptions: () => props.regions.map(r => ({ label: r.name, value: r.id })),
            onSelect: (val) => { onRegionChange(val); }
        },
        inPark: {
            title: '是否在园区',
            getOptions: () => [{ label: '是', value: true }, { label: '否', value: false }],
            onSelect: (val) => { onInParkChange(val); }
        },
        parkOrUnitId: {
            title: computed(() => formData.value.inPark ? '选择园区' : '选择管控单元'),
            getOptions: () => parkOrUnitOptions.value.map(p => ({ label: p.name || p.unit_name, value: p.id })),
        },
        industryKeywords: {
            title: '选择行业关注点',
            getOptions: () => industryKeywords.value.map(k => ({ label: k, value: k })),
            isMulti: true,
            onSelect: () => { onOptionalChange(); }
        },
        generalTags: {
            title: '选择通用关注点',
            getOptions: () => props.generalTags.map(t => ({ label: t.tag_name, value: t.id })),
            isMulti: true,
            onSelect: () => { onOptionalChange(); }
        }
    };
    
    const openDrawer = (key) => {
        const config = drawerConfig[key];
        if (!config) return;
        
        genericDrawer.value.title = typeof config.title === 'string' ? config.title : config.title.value;
        genericDrawer.value.options = config.getOptions();
        genericDrawer.value.targetKey = key;
        genericDrawer.value.isMulti = !!config.isMulti;

        if (genericDrawer.value.isMulti) {
            genericDrawer.value.tempMultiSelection = [...(formData.value[key] || [])];
        }
        genericDrawer.value.visible = true;
    };

    const handleGenericOptionClick = (option) => {
        const key = genericDrawer.value.targetKey;
        const isMulti = genericDrawer.value.isMulti;
        
        if (isMulti) {
            const tempSelection = genericDrawer.value.tempMultiSelection;
            const index = tempSelection.indexOf(option.value);
            if (index > -1) {
                tempSelection.splice(index, 1);
            } else {
                tempSelection.push(option.value);
            }
        } else {
            formData.value[key] = option.value;
            const config = drawerConfig[key];
            if (config.onSelect) {
                config.onSelect(option.value);
            }
            genericDrawer.value.visible = false;
        }
    };

    const confirmMultiSelect = () => {
        const key = genericDrawer.value.targetKey;
        formData.value[key] = [...genericDrawer.value.tempMultiSelection];
        const config = drawerConfig[key];
        if (config.onSelect) config.onSelect(formData.value[key]);
        genericDrawer.value.visible = false;
    };
    
    const cancelMultiSelect = () => {
        genericDrawer.value.visible = false;
    };

    const isOptionSelected = (value) => {
        const { targetKey, isMulti, tempMultiSelection } = genericDrawer.value;
        if (isMulti) {
            return tempMultiSelection.includes(value);
        }
        return formData.value[targetKey] === value;
    };

    // --- Computed properties for display names ---
    const selectedRegionName = computed(() => props.regions.find(r => r.id === formData.value.regionId)?.name);
    const selectedInParkText = computed(() => formData.value.inPark === null ? '请选择' : (formData.value.inPark ? '是' : '否'));
    const selectedParkOrUnitName = computed(() => {
      const selectedOption = parkOrUnitOptions.value.find(p => p.id === formData.value.parkOrUnitId);
      return selectedOption ? (selectedOption.name || selectedOption.unit_name) : '';
    });
    const selectedKeywordsText = computed(() => formData.value.industryKeywords.length > 0 ? `已选 ${formData.value.industryKeywords.length} 个` : '可多选');
    const selectedTagsText = computed(() => formData.value.generalTags.length > 0 ? `已选 ${formData.value.generalTags.length} 个` : '可多选');

    const resetMobileNav = () => {
        mobileNavState.value.path = [];
        mobileNavState.value.currentOptions = props.industryOptions;
        mobileNavState.value.title = '选择行业分类';
    };

    return {
      formData,
      parkOrUnitOptions,
      industryKeywords,
      canSearch,
      summary,
      onRegionChange,
      onInParkChange,
      onIndustryChange,
      onOptionalChange,
      handleSearch,
      handleReset,
      expandForm,
      getFormData,
      isMobile,
      industryDrawerVisible,
      mobileNavState,
      handleMobileOptionClick,
      goUpMobileNav,
      selectedIndustryName,
      openDrawer,
      genericDrawer,
      handleGenericOptionClick,
      isOptionSelected,
      confirmMultiSelect,
      cancelMultiSelect,
      selectedRegionName,
      selectedInParkText,
      selectedParkOrUnitName,
      selectedKeywordsText,
      selectedTagsText,
      mobileSearchKeyword,
      filteredMobileOptions,
      resetMobileSearch,
    };
  },
};
</script>

<style scoped>
.search-container {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-form-card, .collapsed-summary-card {
  background-color: var(--card-bg-color, rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  margin-bottom: 32px;
  border: 1px solid var(--border-color, #e5e9f2);
}

.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.card-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1d2939;
  margin: 0 0 8px;
}

.card-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.form-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #334155;
  margin: 20px 0 16px;
  display: flex;
  align-items: center;
}
.form-section-title:first-of-type {
    margin-top: 0;
}

.form-divider {
    border: none;
    border-top: 1px solid #e2e8f0;
    margin: 28px 0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 20px 24px;
}

.el-form-item {
  margin-bottom: 0;
}

:deep(.el-select), :deep(.el-cascader) {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.el-button {
  border-radius: 10px;
  font-weight: 500;
  --el-button-disabled-bg-color: #f7f9fc;
}
.el-button .el-icon {
  margin-right: 6px;
}
.search-btn .el-icon {
    margin-left: 6px;
    margin-right: 0;
}

/* Improve visibility of disabled primary button, applies to all screen sizes */
.search-btn.is-disabled {
    background-color: #a0cfff;
    border-color: #a0cfff;
}

/* Collapsed State */
.collapsed-summary-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}
.collapsed-summary-card:hover {
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.summary-item {
  color: #475569;
  font-size: 0.95rem;
}
.summary-item strong {
  color: #1d2939;
  font-weight: 500;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .search-form-card, .collapsed-summary-card {
    padding: 24px 16px;
  }
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .collapsed-summary-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  .industry-select-btn {
    width: 100%;
    justify-content: flex-start;
  }
  :deep(.el-divider) {
    margin: 24px 0;
  }
  .form-actions {
    flex-direction: column;
    gap: 12px;
    margin-top: 24px;
  }
  .search-btn, .reset-btn {
    width: 100%;
    margin: 0 !important;
  }
}

.industry-select-btn {
    justify-content: space-between;
    text-align: left;
    font-weight: normal;
    color: var(--el-text-color-primary);
}

.industry-drawer .el-drawer__header {
    margin-bottom: 0;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}
.industry-drawer .el-drawer__header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-color-primary);
}
.industry-drawer .el-drawer__body {
    padding: 0;
}
.drawer-content {
    height: 100%;
    overflow-y: auto;
}
.drawer-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
}
.drawer-option:hover {
    background-color: #f7f9fc;
}
.drawer-option span {
    color: var(--text-color-regular);
}

.generic-drawer .el-drawer__body {
    padding: 0;
}
.generic-drawer .drawer-footer {
    display: flex;
    gap: 16px;
    padding: 16px;
    border-top: 1px solid var(--border-color);
}
.generic-drawer .drawer-footer .el-button {
    flex: 1;
}
.drawer-option.is-selected {
    color: var(--el-color-primary);
    font-weight: 500;
}
.drawer-option.is-selected span {
    color: var(--el-color-primary);
}
.drawer-empty {
    text-align: center;
    color: var(--text-color-secondary);
    padding: 40px;
}

.drawer-search-box {
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-color);
}

.search-form-card {
  background-color: #ffffff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.card-header {
  text-align: center;
  margin-bottom: 28px;
}

.card-title {
  font-size: 1.6rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.card-subtitle {
    margin: 0;
    color: #5a6a7a;
    font-size: 1rem;
}

.form-item-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.divider-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  font-weight: 600;
}

.color-primary { color: #409EFF; }
.color-optional { color: #67c23a; }

.label-icon-primary { color: #409EFF; }
.label-icon-optional { color: #67c23a; }

:deep(.el-divider__text) {
  background-color: #ffffff;
  padding: 0 1em;
}

:deep(.el-divider) {
    margin: 32px 0;
    border-color: #dcdfe6;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 24px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

@media (max-width: 768px) {
    .search-form-card {
        padding: 24px 16px;
    }

    .card-header {
        margin-bottom: 24px;
    }

    .card-title {
        font-size: 1.4rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    :deep(.el-divider) {
        margin: 24px 0;
    }

    .form-actions {
        flex-direction: column;
        gap: 12px;
        margin-top: 24px;
    }

    .search-btn, .reset-btn {
        width: 100%;
        margin: 0 !important;
    }
}

/* Mobile Drawer Styles */
.industry-drawer .el-drawer__header {
    margin-bottom: 0;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}
.industry-drawer .el-drawer__header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-color-primary);
}
.industry-drawer .el-drawer__body {
    padding: 0;
}
.drawer-content {
    height: 100%;
    overflow-y: auto;
}
.drawer-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
}
.drawer-option:hover {
    background-color: #f7f9fc;
}
.drawer-option span {
    color: var(--text-color-regular);
}
</style> 