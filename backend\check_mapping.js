const { initDatabase, getDatabase } = require('./config/database');

async function checkMapping() {
  await initDatabase();
  const db = getDatabase();

  // 查看所有表
  db.all('SELECT name FROM sqlite_master WHERE type="table"', (err, tables) => {
    if (err) {
      console.error('查询表失败:', err);
      return;
    }
    
    console.log('所有表:');
    tables.forEach(table => {
      console.log('  -', table.name);
    });
    
    // 查找可能的关联表
    const possibleMappingTables = tables.filter(t => 
      t.name.includes('mapping') || 
      t.name.includes('relation') || 
      t.name.includes('link') ||
      t.name.includes('industry')
    );
    
    console.log('\n可能的关联表:');
    possibleMappingTables.forEach(table => {
      console.log('  -', table.name);
    });
    
    // 检查industry_classification表中是否有关联字段
    db.all('PRAGMA table_info(industry_classification)', (err, columns) => {
      if (err) {
        console.error('查询字段失败:', err);
        return;
      }
      
      console.log('\nindustry_classification表字段:');
      columns.forEach(col => {
        console.log('  -', col.name, ':', col.type);
      });
      
      // 检查industries表字段
      db.all('PRAGMA table_info(industries)', (err, columns) => {
        if (err) {
          console.error('查询字段失败:', err);
          return;
        }
        
        console.log('\nindustries表字段:');
        columns.forEach(col => {
          console.log('  -', col.name, ':', col.type);
        });
        
        // 查看卷烟制造的具体数据
        console.log('\n=== 卷烟制造相关数据 ===');
        
        // 在industry_classification表中查找卷烟制造
        db.all('SELECT * FROM industry_classification WHERE primary_name LIKE "%卷烟%"', (err, rows) => {
          if (err) {
            console.error('查询失败:', err);
            return;
          }
          
          console.log('\nindustry_classification表中的卷烟制造:');
          rows.forEach(row => {
            console.log('  ID:', row.id, '代码:', row.code, '名称:', row.primary_name);
          });
          
          // 在industries表中查找卷烟制造
          db.all('SELECT * FROM industries WHERE name LIKE "%卷烟%" OR category LIKE "%卷烟%"', (err, rows) => {
            if (err) {
              console.error('查询失败:', err);
              return;
            }
            
            console.log('\nindustries表中的卷烟制造:');
            rows.forEach(row => {
              console.log('  ID:', row.id, '代码:', row.code, '名称:', row.name, '分类:', row.category);
            });
            
            process.exit(0);
          });
        });
      });
    });
  });
}

checkMapping().catch(console.error);
