<template>
  <div class="industries">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>建设项目环境影响评价分类管理名录（2021年版）</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增行业
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="搜索">
            <el-input
              v-model="searchForm.search"
              placeholder="请输入行业名称、代码或分类"
              clearable
              style="width: 300px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 200px">
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="行业代码" width="120" />
        <el-table-column prop="name" label="项目类别" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="大类" width="150" />
        <el-table-column prop="report_book" label="报告书" width="150" show-overflow-tooltip />
        <el-table-column prop="report_table" label="报告表" width="150" show-overflow-tooltip />
        <el-table-column prop="registration_form" label="登记表" width="120" show-overflow-tooltip />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="showViewDialog(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="warning" size="small" @click="showEditDialog(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1000px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="行业代码" prop="code">
                <el-input v-model="formData.code" placeholder="如：一、农业01" />
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="项目类别" prop="name">
                <el-input v-model="formData.name" placeholder="如：农产品初加工项目（含药材基地）" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="大类名称" prop="category">
                <el-input v-model="formData.category" placeholder="如：一、农业" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="大类排序">
                <el-input-number
                  v-model="formData.category_order"
                  :min="1"
                  :max="999"
                  controls-position="right"
                  style="width: 100%"
                  placeholder="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目序号">
                <el-input-number
                  v-model="formData.item_number"
                  :min="1"
                  :max="999"
                  controls-position="right"
                  style="width: 100%"
                  placeholder="1"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 环评要求 -->
        <div class="form-section">
          <h4 class="section-title">环评要求</h4>

          <el-form-item label="报告书">
            <el-input
              v-model="formData.report_book"
              type="textarea"
              :rows="3"
              placeholder="请输入需要编制环境影响报告书的项目情形，如：涉及环境敏感区的"
              show-word-limit
              maxlength="500"
            />
            <div class="form-tip">需要编制环境影响报告书的项目情形</div>
          </el-form-item>

          <el-form-item label="报告表">
            <el-input
              v-model="formData.report_table"
              type="textarea"
              :rows="3"
              placeholder="请输入需要编制环境影响报告表的项目情形，如：涉及环境敏感区的"
              show-word-limit
              maxlength="500"
            />
            <div class="form-tip">需要编制环境影响报告表的项目情形</div>
          </el-form-item>

          <el-form-item label="登记表">
            <el-input
              v-model="formData.registration_form"
              type="textarea"
              :rows="3"
              placeholder="请输入需要填报环境影响登记表的项目情形，如：其他"
              show-word-limit
              maxlength="500"
            />
            <div class="form-tip">需要填报环境影响登记表的项目情形</div>
          </el-form-item>
        </div>

        <!-- 环境敏感区说明 -->
        <div class="form-section">
          <h4 class="section-title">环境敏感区说明</h4>

          <el-form-item label="敏感区含义">
            <el-input
              v-model="formData.sensitive_area_meaning"
              type="textarea"
              :rows="4"
              placeholder="请输入本栏目环境敏感区的具体含义和范围说明"
              show-word-limit
              maxlength="1000"
            />
            <div class="form-tip">本栏目环境敏感区的具体含义和适用范围</div>
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="行业详情"
      width="800px"
    >
      <div v-if="viewData" class="view-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="行业代码">{{ viewData.code }}</el-descriptions-item>
          <el-descriptions-item label="项目类别">{{ viewData.name }}</el-descriptions-item>
          <el-descriptions-item label="大类">{{ viewData.category }}</el-descriptions-item>
          <el-descriptions-item label="大类排序">{{ viewData.category_order }}</el-descriptions-item>
          <el-descriptions-item label="项目序号" :span="2">{{ viewData.item_number }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>报告书</h4>
          <div class="detail-content">{{ viewData.report_book || '/' }}</div>
        </div>

        <div class="detail-section">
          <h4>报告表</h4>
          <div class="detail-content">{{ viewData.report_table || '/' }}</div>
        </div>

        <div class="detail-section">
          <h4>登记表</h4>
          <div class="detail-content">{{ viewData.registration_form || '/' }}</div>
        </div>

        <div class="detail-section">
          <h4>本栏目环境敏感区含义</h4>
          <div class="detail-content">{{ viewData.sensitive_area_meaning || '/' }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, View, Edit, Delete } from '@element-plus/icons-vue'
import { adminApiService } from '@/utils/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const categories = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  search: '',
  category: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentId = ref(null)

// 表单数据
const formData = reactive({
  code: '',
  name: '',
  category: '',
  category_order: 1,
  item_number: 1,
  report_book: '',
  report_table: '',
  registration_form: '',
  sensitive_area_meaning: ''
})

// 查看数据
const viewData = ref(null)

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入项目类别名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请输入大类名称', trigger: 'blur' }
  ]
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await adminApiService.getIndustries(params)
    tableData.value = response.data.data
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await adminApiService.getIndustryCategories()
    categories.value = response.data
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    category: ''
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 选择处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 显示新增对话框
const showCreateDialog = () => {
  dialogTitle.value = '新增行业'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑行业'
  isEdit.value = true
  currentId.value = row.id
  Object.assign(formData, {
    code: row.code,
    name: row.name,
    category: row.category,
    category_order: row.category_order,
    item_number: row.item_number,
    report_book: row.report_book,
    report_table: row.report_table,
    registration_form: row.registration_form,
    sensitive_area_meaning: row.sensitive_area_meaning
  })
  dialogVisible.value = true
}

// 显示查看对话框
const showViewDialog = (row) => {
  viewData.value = row
  viewDialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    category: '',
    category_order: 1,
    item_number: 1,
    report_book: '',
    report_table: '',
    registration_form: '',
    sensitive_area_meaning: ''
  })
}

// 关闭对话框
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isEdit.value) {
          await adminApiService.updateIndustry(currentId.value, formData)
          ElMessage.success('更新成功')
        } else {
          await adminApiService.createIndustry(formData)
          ElMessage.success('创建成功')
        }

        dialogVisible.value = false
        fetchData()
        fetchCategories() // 重新获取分类列表
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除行业"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApiService.deleteIndustry(row.id)
    ElMessage.success('删除成功')
    fetchData()
    fetchCategories() // 重新获取分类列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
  fetchCategories()
})
</script>

<style scoped>
.industries {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.view-content {
  padding: 10px 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.detail-content {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 10px;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.section-title {
  margin: 0 0 16px 0;
  padding: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 数字输入框优化 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
  padding-left: 12px;
  padding-right: 40px;
}

/* 对话框内容区域 */
:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 表单项间距优化 */
:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style>
