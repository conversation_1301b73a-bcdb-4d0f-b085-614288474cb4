# 蓝天Wiki (ltwiki) 项目总结

## 关键理解纠正

### ❌ 之前的错误理解：
- 项目类型和行业是两个不同的概念
- 筛选是平行的多选框组合查询
- 三线一单是一个单独的政策类型

### ✅ 正确的理解：
- **项目类型就是行业！** 没有单独的"项目类型"筛选
- **筛选是递进式的联动筛选**：地区 → 是否在园区 → 园区/单元 → 行业
- **三线一单是预置的复杂数据**，不是用户选择的类型，而是系统默认包含的管控要求

## 核心筛选逻辑

### 必选项（递进式，必须关注的分类）：
```
1. 地区选择
   ├── 普通行政区域：红旗区、卫滨区、牧野区等
   └── 特殊区域：经开区、高新区

2. 是否在园区
   ├── 在园区 → 显示园区列表
   └── 不在园区 → 显示三线一单管控单元列表

3. 园区名称/三线一单管控单元
   └── 根据上一步选择显示对应选项

4. 行业分类
   └── 树形结构：制造业 > 化工 > 电镀
```

### 可选项（补充筛选，可能需要关注的部分）：
```
5. 行业关注关键词
   └── 基于选择的行业自动显示预设关键词

6. 通用关注关键词
   └── 环评、节能评估、安全评价等可选标签
```

## 查询结果分类

### 🔴 必须关注（基于必选项的精准匹配）
- **三线一单管控要求**：对应园区/单元的具体管控要求
- **行业审批原则**：针对选择行业的审批政策
- **园区准入要求**：选择园区的准入限制条件
- **行业规范标准**：相关的技术标准和规范

### 🟡 可能需要关注（基于可选项的关键词匹配）
- 包含行业关键词的相关政策文件
- 包含通用标签的相关政策文件

## 特殊区域处理

### 经开区和高新区的跨区域特性：
- **经开区** 包含：新乡工业产业集聚区、新乡高新技术产业开发区、新乡经济技术开发区
- **高新区** 即为：新乡高新技术产业开发区
- 这些园区可能跨越多个行政区域（如红旗区、延津县等）

## 技术实现要点

### 数据库设计：
- 使用 `region_park_relations` 表处理跨区域园区关系
- 使用 `industry_keywords` 表存储行业预设关键词
- 使用 `general_tags` 和 `policy_tags` 表实现标签系统

### 前端联动：
- 严格按照 地区 → 是否在园区 → 园区/单元 → 行业 的顺序激活选项
- 每一步选择都会重置后续选项并触发相应的数据加载

### 后端查询：
- 必选项查询：精准匹配，返回结构化的分类结果
- 可选项查询：关键词模糊匹配，返回补充信息

## 用户使用场景示例

**场景：** 经开区新乡经济技术开发区的电镀项目

**操作流程：**
1. 选择地区：经开区
2. 选择是否在园区：在园区
3. 选择园区：新乡经济技术开发区
4. 选择行业：制造业 > 化工 > 电镀
5. （可选）选择行业关键词：VOCs排放、重金属污染
6. （可选）选择通用标签：环评、节能评估

**系统返回：**
- 🔴 必须关注：新乡经济技术开发区的三线一单管控单元要求 + 电镀行业审批原则 + 园区准入要求
- 🟡 可能需要关注：包含VOCs排放、重金属污染、环评、节能评估关键词的相关政策

## 部署要求

- **单容器部署**：前后端打包在一个Docker容器中
- **零维护**：使用SQLite数据库，无需数据库管理
- **一键启动**：docker-compose up 即可运行
- **开发友好**：开发阶段前后端都用dev模式，无需构建

这样的设计确保了系统既能满足精准查询的需求，又保持了技术实现的简洁性。 