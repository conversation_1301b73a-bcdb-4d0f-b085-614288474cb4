const { initDatabase, getDatabase } = require('./config/database');

async function checkDataRelationship() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 检查数据关系 ===');
    
    // 1. 查看industries表结构和数据
    console.log('\n--- industries表数据示例 ---');
    const industries = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM industries LIMIT 10', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    industries.forEach(industry => {
      console.log(`ID: ${industry.id}, 名称: ${industry.name}, 代码: ${industry.code || '无'}, 分类: ${industry.category || '无'}`);
    });
    
    // 2. 查看industry_classification表结构和数据
    console.log('\n--- industry_classification表数据示例 ---');
    const classifications = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM industry_classification WHERE level = 1 LIMIT 5', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    classifications.forEach(cls => {
      console.log(`代码: ${cls.code}, 名称: ${cls.primary_name}, 层级: ${cls.level}, 父级: ${cls.parent_code || '无'}`);
    });
    
    // 3. 查看具体的"农产品基地项目"
    console.log('\n--- 查找"农产品基地项目" ---');
    const targetIndustry = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM industries WHERE name = "农产品基地项目（含药材基地）"', (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    if (targetIndustry) {
      console.log('找到目标行业:', targetIndustry);
      
      // 4. 查看这个行业应该对应的分类
      console.log('\n--- 查找对应的行业分类 ---');
      
      // 尝试通过分类查找
      if (targetIndustry.category) {
        const relatedClassifications = await new Promise((resolve, reject) => {
          db.all(`
            SELECT * FROM industry_classification 
            WHERE primary_name LIKE '%${targetIndustry.category}%' 
            ORDER BY level
          `, (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        });
        
        console.log('通过分类查找到的行业分类:');
        relatedClassifications.forEach(cls => {
          console.log(`  代码: ${cls.code}, 名称: ${cls.primary_name}, 层级: ${cls.level}`);
        });
      }
      
      // 尝试通过关键词查找
      const keywordMatches = await new Promise((resolve, reject) => {
        db.all(`
          SELECT * FROM industry_classification 
          WHERE primary_name LIKE '%农业%' OR primary_name LIKE '%种植%' OR primary_name LIKE '%农产品%'
          ORDER BY level
        `, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });
      
      console.log('通过关键词查找到的行业分类:');
      keywordMatches.forEach(cls => {
        console.log(`  代码: ${cls.code}, 名称: ${cls.primary_name}, 层级: ${cls.level}`);
      });
    } else {
      console.log('未找到目标行业');
    }
    
    // 5. 检查表结构
    console.log('\n--- industries表结构 ---');
    const industriesSchema = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(industries)", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    industriesSchema.forEach(col => {
      console.log(`字段: ${col.name}, 类型: ${col.type}`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('检查失败:', error);
    process.exit(1);
  }
}

checkDataRelationship();
