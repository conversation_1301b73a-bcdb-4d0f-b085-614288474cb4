<template>
  <div class="result-item">
    <div class="item-header">
      <h4 class="item-title">{{ policy.policy_title }}</h4>
      <div class="item-tags">
        <el-tag v-for="keyword in policy.matched_keywords" :key="keyword" size="small" type="warning">
          {{ keyword }}
        </el-tag>
        <el-tag v-for="tag in policy.matched_tags" :key="tag" size="small" type="info">
          {{ tag }}
        </el-tag>
      </div>
      <el-link :href="policy.source_file_url" target="_blank" v-if="policy.source_file_url" class="item-link">
        <i class="icon-link"></i>
        查看原文
      </el-link>
    </div>
    <div class="item-content">
      <p class="content-text">{{ policy.rule_content }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PolicyResultItem',
  props: {
    policy: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped>
/* 图标字体 */
.icon-link::before { content: '🔗'; }

/* 结果项目 */
.result-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.result-item:last-child {
  margin-bottom: 0;
}

.item-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.item-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  flex: 1;
  min-width: 200px;
}

.item-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.item-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
}

.item-content {
  padding: 20px;
}

.content-text {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .item-title {
    min-width: auto;
  }
}
</style> 