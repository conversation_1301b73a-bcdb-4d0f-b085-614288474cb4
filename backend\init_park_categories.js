const { initDatabase, getDatabase } = require('./config/database');

async function initParkCategories() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 初始化园区要求分类 ===');
    
    // 检查是否已有分类数据
    const existingCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM park_requirement_categories', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    if (existingCount > 0) {
      console.log('已存在', existingCount, '个分类，跳过初始化');
      process.exit(0);
    }
    
    // 初始分类数据
    const categories = [
      {
        name: '准入要求',
        description: '企业入园的基本准入条件和要求',
        sort_order: 1
      },
      {
        name: '环保要求',
        description: '环境保护相关的要求和标准',
        sort_order: 2
      },
      {
        name: '安全要求',
        description: '安全生产和消防安全相关要求',
        sort_order: 3
      },
      {
        name: '规划要求',
        description: '土地使用和建设规划相关要求',
        sort_order: 4
      },
      {
        name: '产业政策',
        description: '产业发展政策和导向要求',
        sort_order: 5
      },
      {
        name: '投资强度',
        description: '投资规模和强度相关要求',
        sort_order: 6
      },
      {
        name: '税收要求',
        description: '税收贡献和优惠政策要求',
        sort_order: 7
      },
      {
        name: '其他要求',
        description: '其他特殊要求和条件',
        sort_order: 8
      }
    ];
    
    // 插入分类数据
    for (const category of categories) {
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO park_requirement_categories (name, description, sort_order) VALUES (?, ?, ?)',
          [category.name, category.description, category.sort_order],
          function(err) {
            if (err) reject(err);
            else {
              console.log(`✅ 创建分类: ${category.name}`);
              resolve(this.lastID);
            }
          }
        );
      });
    }
    
    console.log('\n=== 初始化完成 ===');
    console.log('成功创建', categories.length, '个园区要求分类');
    
    // 显示创建的分类
    const allCategories = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM park_requirement_categories ORDER BY sort_order', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('\n=== 分类列表 ===');
    allCategories.forEach(cat => {
      console.log(`${cat.sort_order}. ${cat.name} - ${cat.description}`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('初始化失败:', error);
    process.exit(1);
  }
}

initParkCategories();
