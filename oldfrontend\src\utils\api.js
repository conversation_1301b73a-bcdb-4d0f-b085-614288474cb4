import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('API请求:', config.method.toUpperCase(), config.url, config.params || config.data)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API响应:', response.config.url, response.data)
    return response.data
  },
  error => {
    console.error('响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// API接口
export const apiService = {
  // 获取行业列表
  getIndustries() {
    return api.get('/industries')
  },

  // 获取行业环评要求
  getIndustryRequirements(industryId) {
    return api.get(`/industries/${industryId}/requirements`)
  },

  // 获取区域列表
  getRegions() {
    return api.get('/regions')
  },

  // 获取园区列表
  getParks(regionId) {
    return api.get('/parks', { params: { region_id: regionId } })
  },

  // 获取管控单元列表
  getControlUnits(regionId) {
    return api.get('/control-units', { params: { region_id: regionId } })
  },

  // 获取行业关键词
  getIndustryKeywords(industryId) {
    return api.get('/industry-keywords', { params: { industry_id: industryId } })
  },

  // 获取通用标签
  getGeneralTags() {
    return api.get('/general-tags')
  },

  // 必选项查询
  searchRequired(params) {
    return api.get('/search/required', { params })
  },

  // 可选项查询
  searchOptional(params) {
    return api.get('/search/optional', { params })
  },

  // 三线一单查询
  searchSanxianyidan(data) {
    return api.post('/sanxianyidan', data)
  }
}

export default api 