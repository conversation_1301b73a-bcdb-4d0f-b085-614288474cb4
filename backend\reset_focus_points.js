const { initDatabase, getDatabase } = require('./config/database');

async function resetFocusPoints() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 重置关注点系统 ===');
    
    // 1. 删除现有的关注点表
    console.log('删除现有关注点表...');
    await new Promise((resolve, reject) => {
      db.run('DROP TABLE IF EXISTS focus_points', (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // 2. 创建新的表结构
    console.log('创建新的表结构...');
    
    // 通用关注点表
    await new Promise((resolve, reject) => {
      db.run(`
        CREATE TABLE IF NOT EXISTS general_focus_points (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          keywords TEXT,
          sort_order INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT TRUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // 行业关注点表
    await new Promise((resolve, reject) => {
      db.run(`
        CREATE TABLE IF NOT EXISTS industry_focus_points (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          keywords TEXT,
          industry_id INTEGER NOT NULL,
          sort_order INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT TRUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (industry_id) REFERENCES industries(id)
        )
      `, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    console.log('表结构创建完成');
    
    // 3. 插入通用关注点测试数据
    console.log('插入通用关注点数据...');
    const generalPoints = [
      { name: '环境影响评价', description: '建设项目环境影响评价相关要求', keywords: '环评,环境影响评价,EIA', sort_order: 1 },
      { name: '节能评估', description: '固定资产投资项目节能评估相关要求', keywords: '节能,能耗,节能评估', sort_order: 2 },
      { name: '安全评价', description: '建设项目安全评价相关要求', keywords: '安全,安全评价,安全预评价', sort_order: 3 },
      { name: '职业卫生评价', description: '建设项目职业病危害评价相关要求', keywords: '职业卫生,职业病,卫生评价', sort_order: 4 },
      { name: '消防审批', description: '建设工程消防设计审查验收相关要求', keywords: '消防,消防审批,消防验收', sort_order: 5 },
      { name: '规划许可', description: '建设工程规划许可相关要求', keywords: '规划,规划许可,建设工程规划', sort_order: 6 },
      { name: '土地审批', description: '建设用地审批相关要求', keywords: '土地,用地审批,建设用地', sort_order: 7 }
    ];
    
    for (const point of generalPoints) {
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO general_focus_points (name, description, keywords, sort_order, is_active) VALUES (?, ?, ?, ?, ?)',
          [point.name, point.description, point.keywords, point.sort_order, 1],
          function(err) {
            if (err) reject(err);
            else resolve(this.lastID);
          }
        );
      });
    }
    
    console.log('通用关注点数据插入完成，共', generalPoints.length, '条');
    
    // 4. 插入行业关注点测试数据
    console.log('插入行业关注点数据...');
    
    // 先获取一些行业ID用于测试
    const industries = await new Promise((resolve, reject) => {
      db.all('SELECT id, name FROM industries LIMIT 10', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (industries.length > 0) {
      const industryPoints = [
        { name: 'VOCs排放控制', description: '挥发性有机化合物排放控制要求', keywords: 'VOCs,挥发性有机物,废气排放', industry_id: industries[0].id, sort_order: 1 },
        { name: '重金属污染防治', description: '重金属污染防治相关要求', keywords: '重金属,污染防治,土壤污染', industry_id: industries[0].id, sort_order: 2 },
        { name: '废水处理要求', description: '工业废水处理相关要求', keywords: '废水,污水处理,COD,氨氮', industry_id: industries[1].id, sort_order: 1 },
        { name: '危险废物管理', description: '危险废物收集、储存、处置要求', keywords: '危险废物,固废,危废处置', industry_id: industries[1].id, sort_order: 2 },
        { name: '噪声控制', description: '工业噪声控制相关要求', keywords: '噪声,声环境,分贝,噪声防治', industry_id: industries[2].id, sort_order: 1 },
        { name: '粉尘治理', description: '工业粉尘治理相关要求', keywords: '粉尘,颗粒物,除尘,PM', industry_id: industries[2].id, sort_order: 2 }
      ];
      
      for (const point of industryPoints) {
        await new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO industry_focus_points (name, description, keywords, industry_id, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?)',
            [point.name, point.description, point.keywords, point.industry_id, point.sort_order, 1],
            function(err) {
              if (err) reject(err);
              else resolve(this.lastID);
            }
          );
        });
      }
      
      console.log('行业关注点数据插入完成，共', industryPoints.length, '条');
      console.log('关联的行业:', industries.slice(0, 3).map(i => i.name).join(', '));
    } else {
      console.log('未找到行业数据，跳过行业关注点插入');
    }
    
    // 5. 验证数据
    const generalCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM general_focus_points', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    const industryCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM industry_focus_points', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    console.log('\n=== 数据验证 ===');
    console.log('通用关注点数量:', generalCount);
    console.log('行业关注点数量:', industryCount);
    
    console.log('\n=== 重置完成 ===');
    process.exit(0);
    
  } catch (error) {
    console.error('重置失败:', error);
    process.exit(1);
  }
}

resetFocusPoints();
