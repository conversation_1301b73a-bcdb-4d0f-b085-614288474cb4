<template>
  <div class="classifications">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>国民经济行业分类（GB/T 4754-2017）</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增分类
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="搜索">
            <el-input
              v-model="searchForm.search"
              placeholder="请输入行业名称或代码"
              clearable
              style="width: 250px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="层级">
            <el-select v-model="searchForm.level" placeholder="请选择层级" clearable style="width: 120px">
              <el-option label="门类" :value="1" />
              <el-option label="大类" :value="2" />
              <el-option label="中类" :value="3" />
              <el-option label="小类" :value="4" />
            </el-select>
          </el-form-item>
          <el-form-item label="父级代码">
            <el-input
              v-model="searchForm.parent_code"
              placeholder="请输入父级代码"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="primary" @click="showTreeView = !showTreeView">
              <el-icon><List /></el-icon>
              {{ showTreeView ? '列表视图' : '树形视图' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 树形视图 -->
      <div v-if="showTreeView" class="tree-view">
        <el-tree
          :data="treeData"
          :props="treeProps"
          node-key="code"
          :expand-on-click-node="false"
          :load="loadTreeNode"
          lazy
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="node-label">
                <el-tag :type="getLevelTag(data.level)" size="small">{{ data.level_name }}</el-tag>
                {{ data.code }} - {{ data.primary_name }}
              </span>
              <div class="node-actions">
                <el-button type="primary" size="small" @click="showViewDialog(data)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button type="warning" size="small" @click="showEditDialog(data)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(data)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 列表视图 -->
      <div v-else>
        <!-- 数据表格 -->
        <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="code" label="行业代码" width="120" />
          <el-table-column prop="level" label="层级" width="80">
            <template #default="{ row }">
              <el-tag :type="getLevelTag(row.level)" size="small">{{ row.level_name }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="primary_name" label="行业名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="parent_code" label="父级代码" width="120" />
          <el-table-column prop="detailed_description" label="详细描述" width="200" show-overflow-tooltip />
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="showViewDialog(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="warning" size="small" @click="showEditDialog(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="行业代码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入行业代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="层级" prop="level">
              <el-select v-model="formData.level" placeholder="请选择层级" style="width: 100%" @change="handleLevelChange">
                <el-option label="门类" :value="1" />
                <el-option label="大类" :value="2" />
                <el-option label="中类" :value="3" />
                <el-option label="小类" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="层级名称" prop="level_name">
              <el-input v-model="formData.level_name" placeholder="自动填充" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="父级代码" prop="parent_code">
              <el-input v-model="formData.parent_code" placeholder="请输入父级代码（门类可为空）" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="行业名称" prop="primary_name">
          <el-input v-model="formData.primary_name" placeholder="请输入行业名称" />
        </el-form-item>

        <el-form-item label="详细描述">
          <el-input
            v-model="formData.detailed_description"
            type="textarea"
            :rows="3"
            placeholder="请输入详细描述"
          />
        </el-form-item>

        <el-form-item label="完整路径描述">
          <el-input
            v-model="formData.full_path_description"
            type="textarea"
            :rows="2"
            placeholder="请输入完整路径描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="行业分类详情"
      width="800px"
    >
      <div v-if="viewData" class="view-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="行业代码">{{ viewData.code }}</el-descriptions-item>
          <el-descriptions-item label="行业名称">{{ viewData.primary_name }}</el-descriptions-item>
          <el-descriptions-item label="层级">
            <el-tag :type="getLevelTag(viewData.level)">{{ viewData.level_name }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="父级代码">{{ viewData.parent_code || '无' }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>详细描述</h4>
          <div class="detail-content">{{ viewData.detailed_description || '无' }}</div>
        </div>

        <div class="detail-section">
          <h4>完整路径描述</h4>
          <div class="detail-content">{{ viewData.full_path_description || '无' }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, List, View, Edit, Delete } from '@element-plus/icons-vue'
import { adminApiService } from '@/utils/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const treeData = ref([])
const selectedRows = ref([])
const showTreeView = ref(false)

// 搜索表单
const searchForm = reactive({
  search: '',
  level: '',
  parent_code: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentId = ref(null)

// 表单数据
const formData = reactive({
  code: '',
  level: '',
  level_name: '',
  primary_name: '',
  detailed_description: '',
  full_path_description: '',
  parent_code: ''
})

// 查看数据
const viewData = ref(null)

// 表单引用
const formRef = ref()

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'primary_name',
  isLeaf: 'isLeaf'
}

// 层级映射
const levelMap = {
  1: '门类',
  2: '大类',
  3: '中类',
  4: '小类'
}

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入行业代码', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择层级', trigger: 'change' }
  ],
  primary_name: [
    { required: true, message: '请输入行业名称', trigger: 'blur' }
  ]
}

// 获取层级标签样式
const getLevelTag = (level) => {
  switch (level) {
    case 1:
      return 'danger'
    case 2:
      return 'warning'
    case 3:
      return 'success'
    case 4:
      return 'primary'
    default:
      return ''
  }
}

// 层级变化处理
const handleLevelChange = (level) => {
  formData.level_name = levelMap[level] || ''
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await adminApiService.getClassifications(params)
    tableData.value = response.data.data
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 加载树形节点
const loadTreeNode = async (node, resolve) => {
  try {
    const parent_code = node.level === 0 ? null : node.data.code
    const response = await adminApiService.getClassificationTree({ parent_code })
    const children = response.data.map(item => ({
      ...item,
      isLeaf: item.level >= 4 // 小类为叶子节点
    }))
    resolve(children)
  } catch (error) {
    ElMessage.error('加载树形数据失败')
    resolve([])
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    level: '',
    parent_code: ''
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 选择处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 显示新增对话框
const showCreateDialog = () => {
  dialogTitle.value = '新增行业分类'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑行业分类'
  isEdit.value = true
  currentId.value = row.id
  Object.assign(formData, {
    code: row.code,
    level: row.level,
    level_name: row.level_name,
    primary_name: row.primary_name,
    detailed_description: row.detailed_description,
    full_path_description: row.full_path_description,
    parent_code: row.parent_code
  })
  dialogVisible.value = true
}

// 显示查看对话框
const showViewDialog = (row) => {
  viewData.value = row
  viewDialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    code: '',
    level: '',
    level_name: '',
    primary_name: '',
    detailed_description: '',
    full_path_description: '',
    parent_code: ''
  })
}

// 关闭对话框
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isEdit.value) {
          await adminApiService.updateClassification(currentId.value, formData)
          ElMessage.success('更新成功')
        } else {
          await adminApiService.createClassification(formData)
          ElMessage.success('创建成功')
        }

        dialogVisible.value = false
        fetchData()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除行业分类"${row.primary_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApiService.deleteClassification(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.classifications {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.tree-view {
  margin-top: 20px;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 10px;
}

.node-label {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.view-content {
  padding: 10px 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.detail-content {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-tree-node__expand-icon) {
  padding: 6px;
}
</style>
