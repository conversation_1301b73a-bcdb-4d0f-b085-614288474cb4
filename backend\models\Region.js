const { getDatabase } = require('../config/database');

class Region {
  // 获取所有区域
  static getAll() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.all("SELECT * FROM regions ORDER BY id", (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据ID获取区域
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.get("SELECT * FROM regions WHERE id = ?", [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建区域
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, code, region_type } = data;
      db.run(
        "INSERT INTO regions (name, code, region_type) VALUES (?, ?, ?)",
        [name, code, region_type || 'normal'],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, ...data });
          }
        }
      );
    });
  }

  // 更新区域
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, code, region_type } = data;
      db.run(
        "UPDATE regions SET name = ?, code = ?, region_type = ? WHERE id = ?",
        [name, code, region_type, id],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id, ...data });
          }
        }
      );
    });
  }

  // 删除区域
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM regions WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }
}

module.exports = Region;
