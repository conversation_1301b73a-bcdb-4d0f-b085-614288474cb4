const { initDatabase, getDatabase } = require('./config/database');

async function fixIndustryRelations() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 修正行业关注点关联逻辑 ===');
    
    // 1. 删除现有的关联表
    console.log('删除现有关联表...');
    await new Promise((resolve, reject) => {
      db.run('DROP TABLE IF EXISTS industry_focus_point_relations', (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // 2. 创建新的关联表（关联到 industry_classification）
    console.log('创建新的关联表...');
    await new Promise((resolve, reject) => {
      db.run(`
        CREATE TABLE industry_focus_point_relations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          industry_focus_point_id INTEGER NOT NULL,
          industry_classification_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (industry_focus_point_id) REFERENCES industry_focus_points(id) ON DELETE CASCADE,
          FOREIGN KEY (industry_classification_id) REFERENCES industry_classification(id) ON DELETE CASCADE,
          UNIQUE(industry_focus_point_id, industry_classification_id)
        )
      `, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // 3. 添加一些测试数据
    console.log('添加测试关联数据...');
    
    // 获取一些行业分类ID
    const classifications = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, code, primary_name, level 
        FROM industry_classification 
        WHERE level <= 2 
        ORDER BY code 
        LIMIT 10
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    // 获取行业关注点
    const focusPoints = await new Promise((resolve, reject) => {
      db.all('SELECT id, name FROM industry_focus_points WHERE is_active = 1', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (classifications.length > 0 && focusPoints.length > 0) {
      // 为每个关注点关联一些分类
      const relations = [
        // 假设第一个关注点关联到农业相关分类
        { point_id: focusPoints[0].id, classification_id: classifications.find(c => c.primary_name.includes('农业') || c.code.startsWith('A'))?.id },
        // 第二个关注点关联到制造业相关分类
        { point_id: focusPoints[1]?.id, classification_id: classifications.find(c => c.primary_name.includes('制造') || c.code.startsWith('C'))?.id },
        // 第三个关注点关联到建筑业相关分类
        { point_id: focusPoints[2]?.id, classification_id: classifications.find(c => c.primary_name.includes('建筑') || c.code.startsWith('E'))?.id }
      ].filter(r => r.point_id && r.classification_id);
      
      for (const relation of relations) {
        await new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO industry_focus_point_relations (industry_focus_point_id, industry_classification_id) VALUES (?, ?)',
            [relation.point_id, relation.classification_id],
            (err) => {
              if (err) reject(err);
              else resolve();
            }
          );
        });
      }
      
      console.log('添加了', relations.length, '条关联数据');
    }
    
    // 4. 验证结果
    const relationCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM industry_focus_point_relations', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    console.log('\n=== 修正完成 ===');
    console.log('关联数据数量:', relationCount);
    
    // 显示关联情况
    const relations = await new Promise((resolve, reject) => {
      db.all(`
        SELECT ifp.name as point_name, ic.primary_name as classification_name, ic.code, ic.level
        FROM industry_focus_point_relations r
        JOIN industry_focus_points ifp ON r.industry_focus_point_id = ifp.id
        JOIN industry_classification ic ON r.industry_classification_id = ic.id
        ORDER BY ifp.name, ic.code
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('\n=== 关联情况 ===');
    relations.forEach(rel => {
      console.log(`${rel.point_name} -> ${rel.classification_name} (${rel.code}, 层级${rel.level})`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('修正失败:', error);
    process.exit(1);
  }
}

fixIndustryRelations();
