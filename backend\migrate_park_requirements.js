const { initDatabase, getDatabase } = require('./config/database');

async function migrateParkRequirements() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 迁移园区要求数据 ===');
    
    // 1. 添加 category 字段
    console.log('\n1. 添加 category 字段...');
    try {
      await new Promise((resolve, reject) => {
        db.run('ALTER TABLE park_requirements ADD COLUMN category VARCHAR(100)', (err) => {
          if (err && !err.message.includes('duplicate column name')) {
            reject(err);
          } else {
            resolve();
          }
        });
      });
      console.log('✅ category 字段添加成功');
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log('✅ category 字段已存在');
      } else {
        throw error;
      }
    }
    
    // 2. 检查现有数据
    console.log('\n2. 检查现有数据...');
    const requirements = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM park_requirements', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log(`找到 ${requirements.length} 条园区要求记录`);
    
    // 3. 迁移数据：将 requirement_type 映射到 category
    console.log('\n3. 迁移数据...');
    const typeToCategory = {
      'access': '准入要求',
      'environmental': '环保要求', 
      'safety': '安全要求',
      'planning': '规划要求',
      'other': '其他要求',
      'policy': '产业政策',
      'investment': '投资强度',
      'tax': '税收要求'
    };
    
    for (const req of requirements) {
      let category = null;
      
      // 如果已经有category值，跳过
      if (req.category) {
        console.log(`ID ${req.id}: 已有分类 "${req.category}"，跳过`);
        continue;
      }
      
      // 根据requirement_type映射到category
      if (req.requirement_type && typeToCategory[req.requirement_type]) {
        category = typeToCategory[req.requirement_type];
      } else {
        // 根据标题内容智能判断分类
        const title = req.requirement_title.toLowerCase();
        const content = req.requirement_content.toLowerCase();
        
        if (title.includes('准入') || content.includes('准入')) {
          category = '准入要求';
        } else if (title.includes('限制') || content.includes('限制')) {
          category = '准入要求'; // 限制也归类为准入要求
        } else if (title.includes('环保') || content.includes('环保')) {
          category = '环保要求';
        } else if (title.includes('安全') || content.includes('安全')) {
          category = '安全要求';
        } else if (title.includes('规划') || content.includes('规划')) {
          category = '规划要求';
        } else if (title.includes('产业') || content.includes('产业')) {
          category = '产业政策';
        } else if (title.includes('投资') || content.includes('投资')) {
          category = '投资强度';
        } else if (title.includes('税收') || content.includes('税收')) {
          category = '税收要求';
        } else {
          category = '其他要求'; // 默认分类
        }
      }
      
      // 更新记录
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE park_requirements SET category = ? WHERE id = ?',
          [category, req.id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log(`ID ${req.id}: "${req.requirement_title}" -> "${category}"`);
    }
    
    // 4. 验证迁移结果
    console.log('\n4. 验证迁移结果...');
    const updatedRequirements = await new Promise((resolve, reject) => {
      db.all('SELECT id, requirement_title, category FROM park_requirements ORDER BY id', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('\n迁移后的数据:');
    updatedRequirements.forEach(req => {
      console.log(`  ID ${req.id}: ${req.requirement_title} -> ${req.category}`);
    });
    
    // 5. 统计分类分布
    console.log('\n5. 分类分布统计:');
    const categoryStats = await new Promise((resolve, reject) => {
      db.all(
        'SELECT category, COUNT(*) as count FROM park_requirements GROUP BY category ORDER BY count DESC',
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
    
    categoryStats.forEach(stat => {
      console.log(`  ${stat.category}: ${stat.count} 条`);
    });
    
    console.log('\n=== 迁移完成 ===');
    process.exit(0);
    
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

migrateParkRequirements();
