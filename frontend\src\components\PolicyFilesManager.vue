<template>
  <div class="policy-files-manager">
    <div class="header">
      <span class="title">相关政策文件</span>
      <el-button type="primary" size="small" @click="addPolicyFile">
        <el-icon><Plus /></el-icon>
        添加文件
      </el-button>
    </div>
    
    <div v-if="policyFiles.length === 0" class="empty-state">
      <el-empty description="暂无政策文件" :image-size="80" />
    </div>
    
    <div v-else class="files-list">
      <div 
        v-for="(file, index) in policyFiles" 
        :key="index" 
        class="file-item"
      >
        <div class="file-content">
          <div class="file-info">
            <el-icon class="file-icon"><Document /></el-icon>
            <div class="file-details">
              <div class="file-name">{{ file.name || '未命名文件' }}</div>
              <div class="file-url">{{ file.url || '无链接' }}</div>
            </div>
          </div>
          <div class="file-actions">
            <el-button type="primary" size="small" @click="editPolicyFile(index)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="removePolicyFile(index)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑政策文件' : '添加政策文件'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="currentFile"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="文件名称" prop="name">
          <el-input
            v-model="currentFile.name"
            placeholder="请输入政策文件名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="文件链接" prop="url">
          <el-input
            v-model="currentFile.url"
            placeholder="请输入政策文件链接地址"
            type="url"
          />
          <div class="form-tip">
            支持 HTTP/HTTPS 链接，如：https://example.com/policy.pdf
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePolicyFile">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Document, Edit, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const policyFiles = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const editIndex = ref(-1)
const currentFile = ref({
  name: '',
  url: ''
})

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 1, max: 100, message: '文件名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入文件链接', trigger: 'blur' },
    { 
      type: 'url', 
      message: '请输入有效的链接地址', 
      trigger: 'blur',
      transform: (value) => {
        // 如果没有协议，自动添加 https://
        if (value && !value.startsWith('http://') && !value.startsWith('https://')) {
          return 'https://' + value
        }
        return value
      }
    }
  ]
}

// 添加政策文件
const addPolicyFile = () => {
  isEdit.value = false
  editIndex.value = -1
  currentFile.value = { name: '', url: '' }
  dialogVisible.value = true
}

// 编辑政策文件
const editPolicyFile = (index) => {
  isEdit.value = true
  editIndex.value = index
  currentFile.value = { ...policyFiles.value[index] }
  dialogVisible.value = true
}

// 保存政策文件
const savePolicyFile = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 自动添加协议
    if (currentFile.value.url && !currentFile.value.url.startsWith('http://') && !currentFile.value.url.startsWith('https://')) {
      currentFile.value.url = 'https://' + currentFile.value.url
    }
    
    if (isEdit.value) {
      // 编辑模式
      policyFiles.value[editIndex.value] = { ...currentFile.value }
    } else {
      // 添加模式
      policyFiles.value.push({ ...currentFile.value })
    }
    
    emit('update:modelValue', policyFiles.value)
    dialogVisible.value = false
    ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
  } catch (error) {
    // 验证失败
  }
}

// 删除政策文件
const removePolicyFile = async (index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除政策文件"${policyFiles.value[index].name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    policyFiles.value.splice(index, 1)
    emit('update:modelValue', policyFiles.value)
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  policyFiles.value = newValue ? [...newValue] : []
}, { immediate: true, deep: true })
</script>

<style scoped>
.policy-files-manager {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.title {
  font-weight: 500;
  color: #303133;
}

.empty-state {
  padding: 40px 20px;
}

.files-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  color: #409eff;
  font-size: 18px;
  margin-right: 12px;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-url {
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}

.file-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .file-info {
    width: 100%;
  }
  
  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
