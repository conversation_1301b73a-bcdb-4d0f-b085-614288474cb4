<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="admin-sidebar">
        <div class="logo">
          <h2>蓝天Wiki 后台管理</h2>
        </div>
        <el-menu
          :default-active="$route.path"
          router
          class="admin-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/admin">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-sub-menu index="data-management">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>基础数据</span>
            </template>
            <el-menu-item index="/admin/control-units">
              <el-icon><Location /></el-icon>
              <span>三线一单管控单元</span>
            </el-menu-item>
            <el-menu-item index="/admin/industries">
              <el-icon><OfficeBuilding /></el-icon>
              <span>分类管理名录</span>
            </el-menu-item>
            <el-menu-item index="/admin/classifications">
              <el-icon><Menu /></el-icon>
              <span>国民经济行业分类</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="focus-points-management">
            <template #title>
              <el-icon><Star /></el-icon>
              <span>关注点管理</span>
            </template>
            <el-menu-item index="/admin/general-focus-points">
              <el-icon><PriceTag /></el-icon>
              <span>通用关注点</span>
            </el-menu-item>
            <el-menu-item index="/admin/industry-focus-points">
              <el-icon><Tickets /></el-icon>
              <span>行业关注点</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="system-management">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统配置</span>
            </template>
            <el-menu-item index="/admin/parks">
              <el-icon><MapLocation /></el-icon>
              <span>开发区管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/park-requirement-categories">
              <el-icon><FolderOpened /></el-icon>
              <span>园区要求分类</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="admin-header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/admin' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="breadcrumbItems.length > 0" v-for="item in breadcrumbItems" :key="item.path" :to="{ path: item.path }">
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="goToFrontend">
              <el-icon><View /></el-icon>
              前台页面
            </el-button>
          </div>
        </el-header>
        
        <!-- 内容区 -->
        <el-main class="admin-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  House, Document, Location, OfficeBuilding, Menu,
  Setting, MapLocation, Star, View, PriceTag, Tickets, FolderOpened
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 面包屑导航
const breadcrumbItems = computed(() => {
  const path = route.path
  const items = []
  
  if (path.includes('/admin/control-units')) {
    items.push({ path: '/admin/control-units', title: '三线一单管理' })
  } else if (path.includes('/admin/industries')) {
    items.push({ path: '/admin/industries', title: '分类管理名录' })
  } else if (path.includes('/admin/classifications')) {
    items.push({ path: '/admin/classifications', title: '国民经济行业分类' })
  } else if (path.includes('/admin/parks')) {
    items.push({ path: '/admin/parks', title: '开发区设置' })
  } else if (path.includes('/admin/park-requirement-categories')) {
    items.push({ path: '/admin/park-requirement-categories', title: '园区要求分类' })
  } else if (path.includes('/admin/focus-points')) {
    items.push({ path: '/admin/focus-points', title: '关注点管理' })
  }
  
  return items
})

// 跳转到前台页面
const goToFrontend = () => {
  window.open('/', '_blank')
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.admin-sidebar {
  background-color: #304156;
  height: 100vh;
  overflow-y: auto;
}

.logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.logo h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
}

.admin-menu {
  border: none;
}

.admin-header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.admin-main {
  background-color: #f0f2f5;
  padding: 20px;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-sub-menu .el-menu-item) {
  height: 45px;
  line-height: 45px;
  padding-left: 50px !important;
}
</style>
