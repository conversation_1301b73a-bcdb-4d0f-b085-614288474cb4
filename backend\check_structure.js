const { initDatabase, getDatabase } = require('./config/database');

async function checkStructure() {
  await initDatabase();
  const db = getDatabase();
  
  console.log('=== 检查分类管理名录的层级结构 ===');
  
  // 查看有子级的分类
  db.all(`
    SELECT DISTINCT category, COUNT(*) as count 
    FROM industries 
    GROUP BY category 
    HAVING count > 1
    ORDER BY count DESC
    LIMIT 5
  `, (err, categories) => {
    if (err) {
      console.error('查询失败:', err);
      process.exit(1);
    }
    
    console.log('主要分类及其下属行业数量：');
    categories.forEach(cat => {
      console.log(`  - ${cat.category}: ${cat.count} 个行业`);
    });
    
    // 查看具体的层级结构示例
    if (categories.length > 0) {
      const topCategory = categories[0].category;
      console.log(`\n${topCategory} 分类下的行业：`);
      
      db.all('SELECT id, name FROM industries WHERE category = ? LIMIT 5', [topCategory], (err, industries) => {
        if (err) {
          console.error('查询行业失败:', err);
        } else {
          industries.forEach(ind => {
            console.log(`    - ID: ${ind.id}, 名称: ${ind.name}`);
          });
        }
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  });
}

checkStructure().catch(console.error);
