const { initDatabase, getDatabase } = require('./config/database');

async function addParkFiles() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 为开发区添加文件字段 ===');
    
    // 1. 添加 files 字段到 parks 表
    console.log('\n1. 添加 files 字段...');
    try {
      await new Promise((resolve, reject) => {
        db.run('ALTER TABLE parks ADD COLUMN files TEXT', (err) => {
          if (err && !err.message.includes('duplicate column name')) {
            reject(err);
          } else {
            resolve();
          }
        });
      });
      console.log('✅ files 字段添加成功');
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log('✅ files 字段已存在');
      } else {
        throw error;
      }
    }
    
    // 2. 为现有园区添加示例文件数据
    console.log('\n2. 添加示例文件数据...');
    
    const sampleFiles = [
      {
        name: '园区规划文件',
        url: 'https://example.com/park-planning.pdf'
      },
      {
        name: '入园指南',
        url: 'https://example.com/entry-guide.pdf'
      },
      {
        name: '政策文件汇编',
        url: 'https://example.com/policy-collection.pdf'
      }
    ];
    
    // 获取所有园区
    const parks = await new Promise((resolve, reject) => {
      db.all('SELECT id, name FROM parks', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log(`找到 ${parks.length} 个园区`);
    
    // 为每个园区添加示例文件
    for (const park of parks) {
      const filesJson = JSON.stringify(sampleFiles);
      
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE parks SET files = ? WHERE id = ?',
          [filesJson, park.id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log(`✅ 为园区 "${park.name}" 添加了 ${sampleFiles.length} 个示例文件`);
    }
    
    // 3. 验证结果
    console.log('\n3. 验证结果...');
    const updatedParks = await new Promise((resolve, reject) => {
      db.all('SELECT id, name, files FROM parks WHERE files IS NOT NULL', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('\n更新后的园区文件信息:');
    updatedParks.forEach(park => {
      try {
        const files = JSON.parse(park.files || '[]');
        console.log(`  ${park.name}: ${files.length} 个文件`);
        files.forEach((file, index) => {
          console.log(`    ${index + 1}. ${file.name} - ${file.url}`);
        });
      } catch (error) {
        console.log(`  ${park.name}: 文件数据解析失败`);
      }
    });
    
    console.log('\n=== 添加完成 ===');
    process.exit(0);
    
  } catch (error) {
    console.error('添加失败:', error);
    process.exit(1);
  }
}

addParkFiles();
