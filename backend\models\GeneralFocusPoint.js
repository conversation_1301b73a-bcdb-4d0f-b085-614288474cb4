const { getDatabase } = require('../config/database');

class GeneralFocusPoint {
  // 分页获取通用关注点
  static getPaginated(page = 1, pageSize = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const offset = (page - 1) * pageSize;
      
      let whereClause = 'WHERE 1=1';
      let params = [];
      
      // 搜索条件
      if (filters.search) {
        whereClause += ' AND (name LIKE ? OR description LIKE ? OR keywords LIKE ?)';
        params.push(`%${filters.search}%`, `%${filters.search}%`, `%${filters.search}%`);
      }
      
      if (filters.is_active !== undefined && filters.is_active !== '') {
        whereClause += ' AND is_active = ?';
        params.push(filters.is_active);
      }
      
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM general_focus_points ${whereClause}`;
      
      db.get(countSql, params, (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 获取分页数据
        const dataSql = `
          SELECT * FROM general_focus_points 
          ${whereClause}
          ORDER BY sort_order, id
          LIMIT ? OFFSET ?
        `;
        const dataParams = [...params, pageSize, offset];
        
        db.all(dataSql, dataParams, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total: countResult.total,
              page,
              pageSize,
              totalPages: Math.ceil(countResult.total / pageSize)
            });
          }
        });
      });
    });
  }

  // 根据ID获取通用关注点
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.get("SELECT * FROM general_focus_points WHERE id = ?", [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建通用关注点
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, keywords, policy_files, sort_order, is_active } = data;

      const policyFilesJson = policy_files ? JSON.stringify(policy_files) : null;

      db.run(
        `INSERT INTO general_focus_points (name, description, keywords, policy_files, sort_order, is_active)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [name, description, keywords, policyFilesJson, sort_order || 0, is_active !== false],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, ...data });
          }
        }
      );
    });
  }

  // 更新通用关注点
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, keywords, policy_files, sort_order, is_active } = data;

      const policyFilesJson = policy_files ? JSON.stringify(policy_files) : null;

      db.run(
        `UPDATE general_focus_points SET
          name = ?, description = ?, keywords = ?, policy_files = ?,
          sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, description, keywords, policyFilesJson, sort_order, is_active, id],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id, ...data });
          }
        }
      );
    });
  }

  // 删除通用关注点
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM general_focus_points WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }

  // 获取所有启用的通用关注点
  static getActive() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.all(
        "SELECT * FROM general_focus_points WHERE is_active = 1 ORDER BY sort_order, id",
        [],
        (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        }
      );
    });
  }

  // 根据ID数组获取通用关注点
  static getByIds(ids) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      if (!ids || ids.length === 0) {
        resolve([]);
        return;
      }
      
      // 构建IN查询条件
      const placeholders = ids.map(() => '?').join(',');
      const sql = `
        SELECT * FROM general_focus_points 
        WHERE is_active = 1 AND id IN (${placeholders})
        ORDER BY sort_order, id
      `;
      
      db.all(sql, ids, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据关键词搜索通用关注点
  static searchByKeywords(keywords) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      if (!keywords || keywords.length === 0) {
        resolve([]);
        return;
      }
      
      // 构建搜索条件
      const keywordConditions = keywords.map(() => 'keywords LIKE ?').join(' OR ');
      const sql = `
        SELECT * FROM general_focus_points 
        WHERE is_active = 1 AND (${keywordConditions})
        ORDER BY sort_order, id
      `;
      
      const params = keywords.map(keyword => `%${keyword}%`);
      
      db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 更新排序
  static updateSortOrder(updates) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");
        
        let completed = 0;
        let hasError = false;
        
        updates.forEach(({ id, sort_order }) => {
          db.run(
            "UPDATE general_focus_points SET sort_order = ? WHERE id = ?",
            [sort_order, id],
            function(err) {
              if (err && !hasError) {
                hasError = true;
                db.run("ROLLBACK");
                reject(err);
                return;
              }
              
              completed++;
              if (completed === updates.length && !hasError) {
                db.run("COMMIT", (err) => {
                  if (err) {
                    reject(err);
                  } else {
                    resolve({ updated: completed });
                  }
                });
              }
            }
          );
        });
      });
    });
  }
}

module.exports = GeneralFocusPoint;
