<template>
  <div class="parks">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>开发区设置管理</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增开发区
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="搜索">
            <el-input
              v-model="searchForm.search"
              placeholder="请输入开发区名称"
              clearable
              style="width: 250px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="开发区名称" min-width="200" show-overflow-tooltip />
        <el-table-column label="要求数量" width="120">
          <template #default="{ row }">
            <el-tag type="primary">{{ row.requirementCount || 0 }} 个</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="380" fixed="right">
          <template #default="{ row }">
            <el-button type="success" size="small" @click="showRequirementsDialog(row)">
              <el-icon><Setting /></el-icon>
              设置要求
            </el-button>
            <el-button type="primary" size="small" @click="showViewDialog(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="warning" size="small" @click="showEditDialog(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑开发区对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="开发区名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入开发区名称" />
        </el-form-item>

        <el-form-item label="相关文件">
          <div class="files-manager">
            <div class="files-header">
              <span class="files-description">为开发区添加相关文件链接，用户在搜索时可以查看和下载</span>
            </div>
            <div v-for="(file, index) in formData.files" :key="index" class="file-item">
              <div class="file-inputs">
                <el-input
                  v-model="file.name"
                  placeholder="文件名称（如：入园指南、政策文件等）"
                  style="width: 250px; margin-right: 12px;"
                />
                <el-input
                  v-model="file.url"
                  placeholder="文件链接（http://或https://开头）"
                  style="width: 350px; margin-right: 12px;"
                />
              </div>
              <div class="file-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="previewFile(file)"
                  :disabled="!file.url"
                  :icon="View"
                >
                  预览
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="removeFile(index)"
                  :icon="Delete"
                >
                  删除
                </el-button>
              </div>
            </div>
            <div class="add-file-section">
              <el-button
                type="primary"
                size="small"
                @click="addFile"
                :icon="Plus"
              >
                添加文件
              </el-button>
              <span class="add-file-tip">建议添加：入园指南、政策文件、规划文件等</span>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="开发区详情"
      width="800px"
    >
      <div v-if="viewData" class="view-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="开发区名称">{{ viewData.name }}</el-descriptions-item>
          <el-descriptions-item label="要求数量">{{ viewData.requirementCount || 0 }} 个</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(viewData.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(viewData.updated_at) }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="viewRequirements.length > 0" class="requirements-section">
          <h4>开发区要求</h4>
          <div v-for="(req, index) in viewRequirements" :key="req.id" class="requirement-item">
            <div class="requirement-header">
              <span class="requirement-title">{{ index + 1 }}. {{ req.requirement_title }}</span>
              <el-tag :type="getCategoryTagType(req.category)" size="small">
                {{ req.category || '未分类' }}
              </el-tag>
            </div>
            <div class="requirement-content">{{ req.requirement_content }}</div>
          </div>
        </div>
        <div v-else class="no-requirements">
          <el-empty description="暂无设置要求" />
        </div>
      </div>
    </el-dialog>

    <!-- 设置要求对话框 -->
    <el-dialog
      v-model="requirementsDialogVisible"
      :title="`设置开发区要求 - ${currentPark?.name}`"
      width="900px"
      @close="handleRequirementsDialogClose"
    >
      <div class="requirements-management">
        <div class="requirements-header">
          <el-button type="primary" @click="showAddRequirementDialog">
            <el-icon><Plus /></el-icon>
            添加要求
          </el-button>
        </div>

        <div v-if="parkRequirements.length > 0" class="requirements-list">
          <div v-for="(req, index) in parkRequirements" :key="req.id" class="requirement-card">
            <el-card>
              <template #header>
                <div class="requirement-card-header">
                  <div class="requirement-info">
                    <span class="requirement-number">{{ index + 1 }}</span>
                    <span class="requirement-title">{{ req.requirement_title }}</span>
                    <el-tag :type="getCategoryTagType(req.category)" size="small">
                      {{ req.category || '未分类' }}
                    </el-tag>
                  </div>
                  <div class="requirement-actions">
                    <el-button type="warning" size="small" @click="showEditRequirementDialog(req)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button type="danger" size="small" @click="handleDeleteRequirement(req)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
              <div class="requirement-content">{{ req.requirement_content }}</div>
            </el-card>
          </div>
        </div>
        <div v-else class="no-requirements">
          <el-empty description="暂无要求，点击上方按钮添加" />
        </div>
      </div>
    </el-dialog>

    <!-- 添加/编辑要求对话框 -->
    <el-dialog
      v-model="requirementDialogVisible"
      :title="requirementDialogTitle"
      width="700px"
      @close="handleRequirementDialogClose"
    >
      <el-form
        ref="requirementFormRef"
        :model="requirementFormData"
        :rules="requirementFormRules"
        label-width="120px"
      >
        <el-form-item label="要求标题" prop="requirement_title">
          <el-input v-model="requirementFormData.requirement_title" placeholder="请输入要求标题" />
        </el-form-item>

        <el-form-item label="要求分类" prop="category">
          <el-select v-model="requirementFormData.category" placeholder="请选择要求分类" style="width: 100%">
            <el-option
              v-for="category in requirementCategories"
              :key="category.id"
              :label="category.name"
              :value="category.name"
            />
          </el-select>
          <div style="margin-top: 8px;">
            <el-button type="text" size="small" @click="openCategoryManagement">
              <el-icon><Setting /></el-icon>
              管理分类
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="requirementFormData.sort_order" :min="0" style="width: 100%" />
        </el-form-item>

        <el-form-item label="要求内容" prop="requirement_content">
          <el-input
            v-model="requirementFormData.requirement_content"
            type="textarea"
            :rows="5"
            placeholder="请输入详细的要求内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="requirementDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRequirementSubmit" :loading="requirementSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Setting, View, Edit, Delete } from '@element-plus/icons-vue'
import { adminApiService } from '@/utils/api'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const requirementSubmitting = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  search: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const requirementsDialogVisible = ref(false)
const requirementDialogVisible = ref(false)
const dialogTitle = ref('')
const requirementDialogTitle = ref('')
const isEdit = ref(false)
const isEditRequirement = ref(false)
const currentId = ref(null)
const currentRequirementId = ref(null)
const currentPark = ref(null)

// 表单数据
const formData = reactive({
  name: '',
  files: []
})

// 要求表单数据
const requirementFormData = reactive({
  requirement_title: '',
  requirement_content: '',
  category: '',
  sort_order: 0
})

// 要求分类数据
const requirementCategories = ref([])

// 查看数据
const viewData = ref(null)
const viewRequirements = ref([])
const parkRequirements = ref([])

// 表单引用
const formRef = ref()
const requirementFormRef = ref()

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入开发区名称', trigger: 'blur' }
  ]
}

const requirementFormRules = {
  requirement_title: [
    { required: true, message: '请输入要求标题', trigger: 'blur' }
  ],
  requirement_content: [
    { required: true, message: '请输入要求内容', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择要求分类', trigger: 'change' }
  ]
}

// 加载要求分类
const loadRequirementCategories = async () => {
  try {
    const response = await adminApiService.getParkRequirementCategories()
    requirementCategories.value = response.data
  } catch (error) {
    console.error('加载要求分类失败:', error)
    ElMessage.error('加载要求分类失败')
  }
}

// 打开分类管理页面
const openCategoryManagement = () => {
  // 在新窗口打开分类管理页面
  const url = router.resolve({ name: 'ParkRequirementCategories' }).href
  window.open(url, '_blank')
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await adminApiService.getParks(params)

    // 为每个园区获取要求数量
    const parksWithRequirements = await Promise.all(
      response.data.data.map(async (park) => {
        try {
          const reqResponse = await adminApiService.getParkRequirements(park.id)
          return {
            ...park,
            requirementCount: reqResponse.data.length
          }
        } catch (error) {
          return {
            ...park,
            requirementCount: 0
          }
        }
      })
    )

    tableData.value = parksWithRequirements
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    search: ''
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 选择处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 显示新增对话框
const showCreateDialog = () => {
  dialogTitle.value = '新增开发区'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑开发区'
  isEdit.value = true
  currentId.value = row.id

  // 解析文件数据
  let files = []
  try {
    files = row.files ? JSON.parse(row.files) : []
  } catch (error) {
    console.error('解析文件数据失败:', error)
    files = []
  }

  Object.assign(formData, {
    name: row.name,
    files: files
  })
  dialogVisible.value = true
}

// 显示查看对话框
const showViewDialog = async (row) => {
  viewData.value = row
  try {
    const response = await adminApiService.getParkRequirements(row.id)
    viewRequirements.value = response.data
  } catch (error) {
    viewRequirements.value = []
  }
  viewDialogVisible.value = true
}

// 显示要求管理对话框
const showRequirementsDialog = async (row) => {
  currentPark.value = row
  await fetchParkRequirements(row.id)
  requirementsDialogVisible.value = true
}

// 获取园区要求
const fetchParkRequirements = async (parkId) => {
  try {
    const response = await adminApiService.getParkRequirements(parkId)
    parkRequirements.value = response.data.sort((a, b) => a.sort_order - b.sort_order)
  } catch (error) {
    ElMessage.error('获取园区要求失败')
    parkRequirements.value = []
  }
}

// 显示添加要求对话框
const showAddRequirementDialog = () => {
  requirementDialogTitle.value = '添加要求'
  isEditRequirement.value = false
  resetRequirementFormData()
  requirementDialogVisible.value = true
}

// 显示编辑要求对话框
const showEditRequirementDialog = (requirement) => {
  requirementDialogTitle.value = '编辑要求'
  isEditRequirement.value = true
  currentRequirementId.value = requirement.id
  Object.assign(requirementFormData, {
    requirement_title: requirement.requirement_title,
    requirement_content: requirement.requirement_content,
    category: requirement.category,
    sort_order: requirement.sort_order
  })
  requirementDialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    files: []
  })
}

// 添加文件
const addFile = () => {
  formData.files.push({
    name: '',
    url: ''
  })
}

// 删除文件
const removeFile = (index) => {
  formData.files.splice(index, 1)
}

// 预览文件
const previewFile = (file) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

// 获取分类标签类型
const getCategoryTagType = (category) => {
  const typeMap = {
    '准入要求': 'danger',
    '环保要求': 'success',
    '安全要求': 'warning',
    '规划要求': 'warning',
    '产业政策': 'primary',
    '投资强度': 'warning',
    '税收要求': 'success',
    '其他要求': 'warning',
    '限制要求': 'danger',
    '禁止要求': 'danger',
    '鼓励要求': 'success',
    '优先要求': 'primary'
  };
  return typeMap[category] || 'primary';
}

// 重置要求表单数据
const resetRequirementFormData = () => {
  Object.assign(requirementFormData, {
    requirement_title: '',
    requirement_content: '',
    category: '',
    sort_order: 0
  })
}

// 关闭对话框
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

const handleRequirementsDialogClose = () => {
  currentPark.value = null
  parkRequirements.value = []
}

const handleRequirementDialogClose = () => {
  requirementFormRef.value?.resetFields()
  resetRequirementFormData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isEdit.value) {
          await adminApiService.updatePark(currentId.value, formData)
          ElMessage.success('更新成功')
        } else {
          await adminApiService.createPark(formData)
          ElMessage.success('创建成功')
        }

        dialogVisible.value = false
        fetchData()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 提交要求表单
const handleRequirementSubmit = async () => {
  if (!requirementFormRef.value) return

  await requirementFormRef.value.validate(async (valid) => {
    if (valid) {
      requirementSubmitting.value = true
      try {
        if (isEditRequirement.value) {
          await adminApiService.updateParkRequirement(currentRequirementId.value, requirementFormData)
          ElMessage.success('更新成功')
        } else {
          await adminApiService.addParkRequirement(currentPark.value.id, requirementFormData)
          ElMessage.success('添加成功')
        }

        requirementDialogVisible.value = false
        await fetchParkRequirements(currentPark.value.id)
        fetchData() // 刷新主列表的要求数量
      } catch (error) {
        ElMessage.error(isEditRequirement.value ? '更新失败' : '添加失败')
      } finally {
        requirementSubmitting.value = false
      }
    }
  })
}

// 删除开发区
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除开发区"${row.name}"吗？删除后相关要求也会被删除。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApiService.deletePark(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 删除要求
const handleDeleteRequirement = async (requirement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除要求"${requirement.requirement_title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApiService.deleteParkRequirement(requirement.id)
    ElMessage.success('删除成功')
    await fetchParkRequirements(currentPark.value.id)
    fetchData() // 刷新主列表的要求数量
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
  loadRequirementCategories()
})
</script>

<style scoped>
.parks {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.view-content {
  padding: 10px 0;
}

.requirements-section {
  margin-top: 20px;
}

.requirements-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.requirement-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.requirement-title {
  font-weight: 600;
  color: #303133;
}

.requirement-content {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.no-requirements {
  padding: 30px;
  text-align: center;
}

.requirements-management {
  padding: 10px 0;
}

.requirements-header {
  margin-bottom: 20px;
  text-align: right;
}

.requirements-list {
  max-height: 500px;
  overflow-y: auto;
}

.requirement-card {
  margin-bottom: 15px;
}

.requirement-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.requirement-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.requirement-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
}

.requirement-actions {
  display: flex;
  gap: 5px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
}

:deep(.el-card__body) {
  padding: 15px 20px;
}

/* 文件管理样式 */
.files-manager {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.files-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.files-description {
  color: #64748b;
  font-size: 14px;
  line-height: 1.5;
}

.file-item {
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.file-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.file-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.add-file-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  transition: all 0.3s ease;
}

.add-file-section:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.add-file-tip {
  color: #6b7280;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-inputs {
    flex-direction: column;
    align-items: stretch;
  }

  .file-inputs .el-input {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .file-actions {
    justify-content: center;
  }

  .add-file-section {
    flex-direction: column;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
