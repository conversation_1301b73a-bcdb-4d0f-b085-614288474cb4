const { initDatabase, getDatabase } = require('./config/database');

async function fixIndustryFocusPoints() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 修复行业关注点支持多行业关联 ===');
    
    // 1. 创建行业关注点关联表
    console.log('创建行业关注点关联表...');
    await new Promise((resolve, reject) => {
      db.run(`
        CREATE TABLE IF NOT EXISTS industry_focus_point_relations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          industry_focus_point_id INTEGER NOT NULL,
          industry_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (industry_focus_point_id) REFERENCES industry_focus_points(id) ON DELETE CASCADE,
          FOREIGN KEY (industry_id) REFERENCES industries(id) ON DELETE CASCADE,
          UNIQUE(industry_focus_point_id, industry_id)
        )
      `, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // 2. 迁移现有数据到关联表
    console.log('迁移现有数据...');
    const existingPoints = await new Promise((resolve, reject) => {
      db.all('SELECT id, industry_id FROM industry_focus_points WHERE industry_id IS NOT NULL', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    for (const point of existingPoints) {
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT OR IGNORE INTO industry_focus_point_relations (industry_focus_point_id, industry_id) VALUES (?, ?)',
          [point.id, point.industry_id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }
    
    console.log('迁移了', existingPoints.length, '条关联数据');
    
    // 3. 删除 industry_focus_points 表中的 industry_id 字段（SQLite不支持DROP COLUMN，所以重建表）
    console.log('重建行业关注点表...');
    
    // 备份数据
    const backupData = await new Promise((resolve, reject) => {
      db.all('SELECT id, name, description, keywords, sort_order, is_active, created_at, updated_at FROM industry_focus_points', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    // 删除旧表
    await new Promise((resolve, reject) => {
      db.run('DROP TABLE industry_focus_points', (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // 创建新表（不包含industry_id字段）
    await new Promise((resolve, reject) => {
      db.run(`
        CREATE TABLE industry_focus_points (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          keywords TEXT,
          sort_order INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT TRUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // 恢复数据
    for (const point of backupData) {
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO industry_focus_points (id, name, description, keywords, sort_order, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          [point.id, point.name, point.description, point.keywords, point.sort_order, point.is_active, point.created_at, point.updated_at],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }
    
    console.log('恢复了', backupData.length, '条行业关注点数据');
    
    // 4. 添加一些测试数据，展示多行业关联
    console.log('添加多行业关联测试数据...');
    
    // 获取一些行业ID
    const industries = await new Promise((resolve, reject) => {
      db.all('SELECT id, name FROM industries LIMIT 10', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (industries.length >= 3) {
      // 创建一个可以关联多个行业的关注点
      const multiIndustryPoint = await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO industry_focus_points (name, description, keywords, sort_order, is_active) VALUES (?, ?, ?, ?, ?)',
          ['环保设施运行管理', '环保设施的日常运行、维护和管理要求', '环保设施,运行管理,维护,监测', 10, 1],
          function(err) {
            if (err) reject(err);
            else resolve(this.lastID);
          }
        );
      });
      
      // 关联到多个行业
      for (let i = 0; i < Math.min(3, industries.length); i++) {
        await new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO industry_focus_point_relations (industry_focus_point_id, industry_id) VALUES (?, ?)',
            [multiIndustryPoint, industries[i].id],
            (err) => {
              if (err) reject(err);
              else resolve();
            }
          );
        });
      }
      
      console.log('创建了多行业关联关注点，关联到', Math.min(3, industries.length), '个行业');
    }
    
    // 5. 验证结果
    const pointCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM industry_focus_points', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    const relationCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM industry_focus_point_relations', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    console.log('\n=== 修复完成 ===');
    console.log('行业关注点数量:', pointCount);
    console.log('行业关联数量:', relationCount);
    
    // 显示关联情况
    const relations = await new Promise((resolve, reject) => {
      db.all(`
        SELECT ifp.name as point_name, i.name as industry_name
        FROM industry_focus_point_relations r
        JOIN industry_focus_points ifp ON r.industry_focus_point_id = ifp.id
        JOIN industries i ON r.industry_id = i.id
        ORDER BY ifp.name, i.name
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('\n=== 关联情况 ===');
    relations.forEach(rel => {
      console.log(`${rel.point_name} -> ${rel.industry_name}`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('修复失败:', error);
    process.exit(1);
  }
}

fixIndustryFocusPoints();
