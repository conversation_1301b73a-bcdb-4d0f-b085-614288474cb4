<template>
  <div class="focus-points">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>关注点管理</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增关注点
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="搜索">
            <el-input
              v-model="searchForm.search"
              placeholder="请输入关注点名称、描述或关键词"
              clearable
              style="width: 300px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="searchForm.point_type" placeholder="请选择类型" clearable style="width: 150px">
              <el-option label="通用关注点" value="general" />
              <el-option label="行业关注点" value="industry" />
            </el-select>
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 150px">
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.is_active" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column prop="name" label="关注点名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="point_type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="row.point_type === 'general' ? 'primary' : 'success'">
              {{ row.point_type === 'general' ? '通用关注点' : '行业关注点' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="keywords" label="关键词" width="200" show-overflow-tooltip />
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="showViewDialog(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="warning" size="small" @click="showEditDialog(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedRows.length > 0" class="batch-actions">
        <el-alert
          :title="`已选择 ${selectedRows.length} 项`"
          type="info"
          show-icon
          :closable="false"
        >
          <template #default>
            <div class="batch-buttons">
              <el-button type="success" size="small" @click="handleBatchEnable">
                批量启用
              </el-button>
              <el-button type="warning" size="small" @click="handleBatchDisable">
                批量禁用
              </el-button>
              <el-button type="danger" size="small" @click="handleBatchDelete">
                批量删除
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关注点名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入关注点名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="point_type">
              <el-select v-model="formData.point_type" placeholder="请选择类型" style="width: 100%">
                <el-option label="通用关注点" value="general" />
                <el-option label="行业关注点" value="industry" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-input v-model="formData.category" placeholder="请输入分类" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联行业" prop="industry_id" v-if="formData.point_type === 'industry'">
              <el-select v-model="formData.industry_id" placeholder="请选择关联行业" style="width: 100%" filterable>
                <el-option
                  v-for="industry in industries"
                  :key="industry.id"
                  :label="industry.name"
                  :value="industry.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number v-model="formData.sort_order" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-switch
                v-model="formData.is_active"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入关注点描述"
          />
        </el-form-item>

        <el-form-item label="关键词">
          <el-input
            v-model="formData.keywords"
            type="textarea"
            :rows="2"
            placeholder="请输入关键词，多个关键词用逗号分隔"
          />
          <div class="form-tip">
            关键词用于匹配用户输入的行业信息，多个关键词请用逗号分隔
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="关注点详情"
      width="600px"
    >
      <div v-if="viewData" class="view-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="关注点名称">{{ viewData.name }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="viewData.point_type === 'general' ? 'primary' : 'success'">
              {{ viewData.point_type === 'general' ? '通用关注点' : '行业关注点' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分类">{{ viewData.category || '无' }}</el-descriptions-item>
          <el-descriptions-item label="关联行业" v-if="viewData.point_type === 'industry'">
            {{ viewData.industry_name || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="排序">{{ viewData.sort_order }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="viewData.is_active ? 'success' : 'danger'">
              {{ viewData.is_active ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(viewData.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(viewData.updated_at) }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>描述</h4>
          <div class="detail-content">{{ viewData.description || '无' }}</div>
        </div>

        <div class="detail-section">
          <h4>关键词</h4>
          <div class="keywords-display">
            <el-tag
              v-for="keyword in getKeywordArray(viewData.keywords)"
              :key="keyword"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ keyword }}
            </el-tag>
            <span v-if="!viewData.keywords">无</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, View, Edit, Delete } from '@element-plus/icons-vue'
import { adminApiService } from '@/utils/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const categories = ref([])
const industries = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  search: '',
  category: '',
  point_type: '',
  is_active: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentId = ref(null)

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  category: '',
  keywords: '',
  point_type: 'general',
  industry_id: null,
  sort_order: 0,
  is_active: true
})

// 查看数据
const viewData = ref(null)

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入关注点名称', trigger: 'blur' }
  ],
  point_type: [
    { required: true, message: '请选择关注点类型', trigger: 'change' }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

// 获取关键词数组
const getKeywordArray = (keywords) => {
  if (!keywords) return []
  return keywords.split(',').map(k => k.trim()).filter(k => k)
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await adminApiService.getFocusPoints(params)
    tableData.value = response.data.data
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await adminApiService.getFocusPointCategories()
    categories.value = response.data
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  }
}

// 获取行业列表
const fetchIndustries = async () => {
  try {
    const response = await adminApiService.getIndustries({ pageSize: 1000 })
    industries.value = response.data.data
  } catch (error) {
    ElMessage.error('获取行业列表失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    category: '',
    point_type: '',
    is_active: ''
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 选择处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 状态变更处理
const handleStatusChange = async (row) => {
  try {
    await adminApiService.updateFocusPoint(row.id, {
      ...row,
      is_active: row.is_active
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.is_active = row.is_active === 1 ? 0 : 1
  }
}

// 显示新增对话框
const showCreateDialog = () => {
  dialogTitle.value = '新增关注点'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑关注点'
  isEdit.value = true
  currentId.value = row.id
  Object.assign(formData, {
    name: row.name,
    description: row.description,
    category: row.category,
    keywords: row.keywords,
    point_type: row.point_type || 'general',
    industry_id: row.industry_id,
    sort_order: row.sort_order,
    is_active: Boolean(row.is_active)
  })
  dialogVisible.value = true
}

// 显示查看对话框
const showViewDialog = (row) => {
  viewData.value = row
  viewDialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    category: '',
    keywords: '',
    point_type: 'general',
    industry_id: null,
    sort_order: 0,
    is_active: true
  })
}

// 关闭对话框
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (isEdit.value) {
          await adminApiService.updateFocusPoint(currentId.value, formData)
          ElMessage.success('更新成功')
        } else {
          await adminApiService.createFocusPoint(formData)
          ElMessage.success('创建成功')
        }

        dialogVisible.value = false
        fetchData()
        fetchCategories() // 重新获取分类列表
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除关注点"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApiService.deleteFocusPoint(row.id)
    ElMessage.success('删除成功')
    fetchData()
    fetchCategories() // 重新获取分类列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量启用
const handleBatchEnable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要启用选中的 ${selectedRows.value.length} 个关注点吗？`,
      '确认批量启用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const promises = selectedRows.value.map(row =>
      adminApiService.updateFocusPoint(row.id, { ...row, is_active: true })
    )

    await Promise.all(promises)
    ElMessage.success('批量启用成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量启用失败')
    }
  }
}

// 批量禁用
const handleBatchDisable = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的 ${selectedRows.value.length} 个关注点吗？`,
      '确认批量禁用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedRows.value.map(row =>
      adminApiService.updateFocusPoint(row.id, { ...row, is_active: false })
    )

    await Promise.all(promises)
    ElMessage.success('批量禁用成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量禁用失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个关注点吗？此操作不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'danger'
      }
    )

    const promises = selectedRows.value.map(row =>
      adminApiService.deleteFocusPoint(row.id)
    )

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    fetchData()
    fetchCategories() // 重新获取分类列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
  fetchCategories()
  fetchIndustries()
})
</script>

<style scoped>
.focus-points {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.batch-actions {
  margin-top: 15px;
}

.batch-buttons {
  margin-top: 10px;
}

.batch-buttons .el-button {
  margin-right: 10px;
}

.view-content {
  padding: 10px 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.detail-content {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.keywords-display {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  min-height: 40px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

:deep(.el-switch) {
  --el-switch-on-color: #13ce66;
  --el-switch-off-color: #ff4949;
}

:deep(.el-table .cell) {
  padding: 8px 10px;
}
</style>
