const { initDatabase } = require('./config/database');
const IndustryClassification = require('./models/IndustryClassification');
const IndustryFocusPoint = require('./models/IndustryFocusPoint');

async function testHierarchyMatching() {
  try {
    await initDatabase();
    
    console.log('=== 测试层级匹配功能 ===');
    
    // 1. 获取一些测试用的分类ID
    const classifications = await IndustryClassification.getCascadeData();
    
    if (classifications.length === 0) {
      console.log('没有找到行业分类数据');
      process.exit(1);
    }
    
    // 找到农业相关的分类
    const agriculture = classifications.find(c => c.name.includes('农') || c.code === 'A');
    
    if (!agriculture) {
      console.log('没有找到农业分类');
      process.exit(1);
    }
    
    console.log('找到农业分类:', agriculture.name, '(ID:', agriculture.id, ')');
    
    // 2. 测试获取分类信息
    console.log('\n--- 测试获取分类信息 ---');
    const classificationInfo = await IndustryClassification.getClassificationInfo(agriculture.id);
    console.log('分类信息:', classificationInfo);
    
    // 3. 测试获取匹配的关注点
    console.log('\n--- 测试获取匹配的关注点 ---');
    const focusPoints = await IndustryFocusPoint.getByClassificationInfo(classificationInfo);
    console.log('匹配的关注点数量:', focusPoints.length);
    
    focusPoints.forEach(point => {
      console.log(`- ${point.name} (关联分类: ${point.industry_names})`);
    });
    
    // 4. 如果有子分类，测试子分类的匹配
    if (agriculture.children && agriculture.children.length > 0) {
      console.log('\n--- 测试子分类匹配 ---');
      const subClassification = agriculture.children[0];
      console.log('测试子分类:', subClassification.name, '(ID:', subClassification.id, ')');
      
      const subClassificationInfo = await IndustryClassification.getClassificationInfo(subClassification.id);
      const subFocusPoints = await IndustryFocusPoint.getByClassificationInfo(subClassificationInfo);
      
      console.log('子分类匹配的关注点数量:', subFocusPoints.length);
      subFocusPoints.forEach(point => {
        console.log(`- ${point.name} (关联分类: ${point.industry_names})`);
      });
      
      // 验证层级匹配：子分类应该能匹配到父分类的关注点
      const shouldMatch = focusPoints.some(parentPoint => 
        subFocusPoints.some(subPoint => subPoint.id === parentPoint.id)
      );
      
      console.log('层级匹配验证:', shouldMatch ? '✅ 成功' : '❌ 失败');
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testHierarchyMatching();
