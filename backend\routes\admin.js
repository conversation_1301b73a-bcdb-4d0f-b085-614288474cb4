const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/adminController');

// 三线一单管理
router.get('/control-units', AdminController.getControlUnits);
router.get('/control-units/:id', AdminController.getControlUnit);
router.post('/control-units', AdminController.createControlUnit);
router.put('/control-units/:id', AdminController.updateControlUnit);
router.delete('/control-units/:id', AdminController.deleteControlUnit);

// 分类管理名录管理
router.get('/industries', AdminController.getIndustries);
router.get('/industries/categories', AdminController.getIndustryCategories);
router.get('/industries/:id', AdminController.getIndustry);
router.post('/industries', AdminController.createIndustry);
router.put('/industries/:id', AdminController.updateIndustry);
router.delete('/industries/:id', AdminController.deleteIndustry);

// 国民经济行业分类管理
router.get('/classifications', AdminController.getClassifications);
router.get('/classifications/tree', AdminController.getClassificationTree);
router.get('/classifications/:id', AdminController.getClassification);
router.post('/classifications', AdminController.createClassification);
router.put('/classifications/:id', AdminController.updateClassification);
router.delete('/classifications/:id', AdminController.deleteClassification);

// 开发区管理
router.get('/parks', AdminController.getParks);
router.get('/parks/:id', AdminController.getPark);
router.post('/parks', AdminController.createPark);
router.put('/parks/:id', AdminController.updatePark);
router.delete('/parks/:id', AdminController.deletePark);

// 园区要求管理
router.get('/parks/:id/requirements', AdminController.getParkRequirements);
router.post('/parks/:id/requirements', AdminController.addParkRequirement);
router.put('/park-requirements/:requirementId', AdminController.updateParkRequirement);
router.delete('/park-requirements/:requirementId', AdminController.deleteParkRequirement);

// 关注点管理（旧版，保留兼容性）
router.get('/focus-points', AdminController.getFocusPoints);
router.get('/focus-points/categories', AdminController.getFocusPointCategories);
router.get('/focus-points/:id', AdminController.getFocusPoint);
router.post('/focus-points', AdminController.createFocusPoint);
router.put('/focus-points/:id', AdminController.updateFocusPoint);
router.delete('/focus-points/:id', AdminController.deleteFocusPoint);
router.put('/focus-points/sort-order', AdminController.updateFocusPointSortOrder);

// 通用关注点管理
router.get('/general-focus-points', AdminController.getGeneralFocusPoints);
router.get('/general-focus-points/:id', AdminController.getGeneralFocusPoint);
router.post('/general-focus-points', AdminController.createGeneralFocusPoint);
router.put('/general-focus-points/:id', AdminController.updateGeneralFocusPoint);
router.delete('/general-focus-points/:id', AdminController.deleteGeneralFocusPoint);

// 行业关注点管理
router.get('/industry-focus-points', AdminController.getIndustryFocusPoints);
router.get('/industry-focus-points/:id', AdminController.getIndustryFocusPoint);
router.post('/industry-focus-points', AdminController.createIndustryFocusPoint);
router.put('/industry-focus-points/:id', AdminController.updateIndustryFocusPoint);
router.delete('/industry-focus-points/:id', AdminController.deleteIndustryFocusPoint);

// 辅助接口
router.get('/regions', AdminController.getRegions);

// 园区要求分类管理路由
router.get('/park-requirement-categories', AdminController.getParkRequirementCategories);
router.post('/park-requirement-categories', AdminController.createParkRequirementCategory);
router.put('/park-requirement-categories/:id', AdminController.updateParkRequirementCategory);
router.delete('/park-requirement-categories/:id', AdminController.deleteParkRequirementCategory);

module.exports = router;
