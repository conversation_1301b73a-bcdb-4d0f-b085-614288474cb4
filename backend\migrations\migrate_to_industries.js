const { initDatabase, getDatabase } = require('../config/database');

async function migrateToIndustries() {
  await initDatabase();
  const db = getDatabase();

  console.log('开始迁移行业关注点数据库结构...');

  return new Promise((resolve, reject) => {
    db.serialize(() => {
      db.run("BEGIN TRANSACTION");

      // 1. 创建新的关联表结构
      console.log('1. 创建新的关联表结构...');
      db.run(`
        CREATE TABLE IF NOT EXISTS industry_focus_point_relations_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          industry_focus_point_id INTEGER NOT NULL,
          industry_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (industry_focus_point_id) REFERENCES industry_focus_points(id) ON DELETE CASCADE,
          FOREIGN KEY (industry_id) REFERENCES industries(id) ON DELETE CASCADE,
          UNIQUE(industry_focus_point_id, industry_id)
        )
      `, (err) => {
        if (err) {
          console.error('创建新表失败:', err);
          db.run("ROLLBACK");
          reject(err);
          return;
        }

        // 2. 迁移数据：从国民经济行业分类映射到分类管理名录
        console.log('2. 开始数据迁移...');
        
        // 获取所有现有的关联关系
        db.all(`
          SELECT r.industry_focus_point_id, r.industry_classification_id, ic.primary_name, ic.code
          FROM industry_focus_point_relations r
          LEFT JOIN industry_classification ic ON r.industry_classification_id = ic.id
        `, (err, relations) => {
          if (err) {
            console.error('查询现有关联失败:', err);
            db.run("ROLLBACK");
            reject(err);
            return;
          }

          console.log(`找到 ${relations.length} 个现有关联关系`);

          if (relations.length === 0) {
            // 没有数据需要迁移，直接替换表
            finalizeMigration();
            return;
          }

          let processed = 0;
          let migrated = 0;
          let failed = 0;

          relations.forEach(relation => {
            // 尝试通过代码匹配
            db.get(`
              SELECT id, name FROM industries WHERE code = ?
            `, [relation.code], (err, industry) => {
              if (err) {
                console.error(`查询行业失败 (代码: ${relation.code}):`, err);
                processed++;
                failed++;
                checkComplete();
                return;
              }

              if (industry) {
                // 通过代码找到了对应的行业
                insertNewRelation(relation.industry_focus_point_id, industry.id, `代码匹配: ${relation.code} -> ${industry.name}`);
              } else {
                // 尝试通过名称匹配
                const cleanName = relation.primary_name ? relation.primary_name.replace(/（[^）]+）/g, '').trim() : '';
                
                if (cleanName) {
                  db.get(`
                    SELECT id, name FROM industries WHERE name LIKE ? OR name LIKE ?
                  `, [`%${cleanName}%`, `%${relation.primary_name}%`], (err, industry) => {
                    if (err) {
                      console.error(`名称匹配查询失败 (${relation.primary_name}):`, err);
                      processed++;
                      failed++;
                      checkComplete();
                      return;
                    }

                    if (industry) {
                      insertNewRelation(relation.industry_focus_point_id, industry.id, `名称匹配: ${relation.primary_name} -> ${industry.name}`);
                    } else {
                      console.warn(`无法匹配行业: ${relation.primary_name} (代码: ${relation.code})`);
                      processed++;
                      failed++;
                      checkComplete();
                    }
                  });
                } else {
                  console.warn(`行业名称为空，跳过: ID ${relation.industry_classification_id}`);
                  processed++;
                  failed++;
                  checkComplete();
                }
              }
            });
          });

          function insertNewRelation(focusPointId, industryId, matchInfo) {
            db.run(`
              INSERT OR IGNORE INTO industry_focus_point_relations_new 
              (industry_focus_point_id, industry_id) 
              VALUES (?, ?)
            `, [focusPointId, industryId], (err) => {
              if (err) {
                console.error(`插入新关联失败 (${matchInfo}):`, err);
                failed++;
              } else {
                console.log(`✓ 迁移成功: ${matchInfo}`);
                migrated++;
              }
              processed++;
              checkComplete();
            });
          }

          function checkComplete() {
            if (processed === relations.length) {
              console.log(`\n迁移完成统计:`);
              console.log(`- 总数: ${relations.length}`);
              console.log(`- 成功: ${migrated}`);
              console.log(`- 失败: ${failed}`);
              
              finalizeMigration();
            }
          }
        });

        function finalizeMigration() {
          // 3. 删除旧表，重命名新表
          console.log('3. 更新表结构...');
          
          db.run("DROP TABLE IF EXISTS industry_focus_point_relations", (err) => {
            if (err) {
              console.error('删除旧表失败:', err);
              db.run("ROLLBACK");
              reject(err);
              return;
            }

            db.run(`
              ALTER TABLE industry_focus_point_relations_new 
              RENAME TO industry_focus_point_relations
            `, (err) => {
              if (err) {
                console.error('重命名表失败:', err);
                db.run("ROLLBACK");
                reject(err);
                return;
              }

              db.run("COMMIT", (err) => {
                if (err) {
                  console.error('提交事务失败:', err);
                  reject(err);
                } else {
                  console.log('✅ 数据库迁移完成！');
                  resolve();
                }
              });
            });
          });
        }
      });
    });
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateToIndustries()
    .then(() => {
      console.log('迁移成功完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('迁移失败:', error);
      process.exit(1);
    });
}

module.exports = { migrateToIndustries };
