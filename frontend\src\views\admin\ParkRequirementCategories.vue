<template>
  <div class="park-requirement-categories-page">
    <div class="page-header">
      <h1>园区要求分类管理</h1>
      <el-button type="primary" @click="openDialog()">
        <el-icon><Plus /></el-icon> 添加分类
      </el-button>
    </div>

    <el-card class="table-card">
      <el-table
        :data="categories"
        style="width: 100%"
        v-loading="loading"
        border
        stripe
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="分类名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="sort_order" label="排序" width="100" />
        <el-table-column label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="openDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑分类' : '添加分类'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="form.sort_order" :min="0" :max="999" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { adminApiService } from '@/utils/api'
import { formatDate } from '@/utils/format'

export default {
  name: 'ParkRequirementCategories',
  components: {
    Plus
  },
  setup() {
    const categories = ref([])
    const loading = ref(false)
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const submitting = ref(false)
    const formRef = ref(null)

    const form = reactive({
      id: null,
      name: '',
      description: '',
      sort_order: 0
    })

    const rules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ]
    }

    // 加载分类列表
    const loadCategories = async () => {
      loading.value = true
      try {
        const response = await adminApiService.getParkRequirementCategories()
        categories.value = response.data
      } catch (error) {
        ElMessage.error('加载分类列表失败')
      } finally {
        loading.value = false
      }
    }

    // 打开对话框
    const openDialog = (row) => {
      resetForm()
      if (row) {
        isEdit.value = true
        Object.assign(form, row)
      } else {
        isEdit.value = false
      }
      dialogVisible.value = true
    }

    // 重置表单
    const resetForm = () => {
      form.id = null
      form.name = ''
      form.description = ''
      form.sort_order = 0
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (!valid) return
        
        submitting.value = true
        try {
          if (isEdit.value) {
            await adminApiService.updateParkRequirementCategory(form.id, form)
            ElMessage.success('更新成功')
          } else {
            await adminApiService.createParkRequirementCategory(form)
            ElMessage.success('添加成功')
          }
          dialogVisible.value = false
          loadCategories()
        } catch (error) {
          ElMessage.error(error.response?.data?.message || '操作失败')
        } finally {
          submitting.value = false
        }
      })
    }

    // 删除分类
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        '确定要删除该分类吗？如果有园区要求使用此分类，将无法删除。',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            await adminApiService.deleteParkRequirementCategory(row.id)
            ElMessage.success('删除成功')
            loadCategories()
          } catch (error) {
            ElMessage.error(error.response?.data?.message || '删除失败')
          }
        })
        .catch(() => {})
    }

    onMounted(() => {
      loadCategories()
    })

    return {
      categories,
      loading,
      dialogVisible,
      isEdit,
      submitting,
      form,
      rules,
      formRef,
      openDialog,
      handleSubmit,
      handleDelete,
      formatDate
    }
  }
}
</script>

<style scoped>
.park-requirement-categories-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 24px;
  margin: 0;
}

.table-card {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
