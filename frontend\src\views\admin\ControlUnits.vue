<template>
  <div class="control-units">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>三线一单管控单元管理</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增管控单元
          </el-button>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="搜索">
            <el-input
              v-model="searchForm.search"
              placeholder="请输入管控单元名称或编码"
              clearable
              style="width: 250px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="区域">
            <el-select v-model="searchForm.region_id" placeholder="请选择区域" clearable style="width: 150px">
              <el-option
                v-for="region in regions"
                :key="region.id"
                :label="region.name"
                :value="region.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-input
              v-model="searchForm.unit_type"
              placeholder="请输入类型关键词"
              clearable
              style="width: 150px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="unit_code" label="管控单元编码" width="150" />
        <el-table-column prop="unit_name" label="管控单元名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="region_name" label="所属区域" width="120" />
        <el-table-column prop="park_name" label="所属园区" width="150" show-overflow-tooltip />
        <el-table-column prop="unit_type" label="单元类型" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getUnitTypeTag(row.unit_type)"
              size="small"
            >
              {{ row.unit_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="showViewDialog(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="warning" size="small" @click="showEditDialog(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="管控单元编码" prop="unit_code">
              <el-input v-model="formData.unit_code" placeholder="请输入管控单元编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管控单元名称" prop="unit_name">
              <el-input v-model="formData.unit_name" placeholder="请输入管控单元名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属区域" prop="region_id">
              <el-select v-model="formData.region_id" placeholder="请选择区域" style="width: 100%">
                <el-option
                  v-for="region in regions"
                  :key="region.id"
                  :label="region.name"
                  :value="region.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单元类型" prop="unit_type">
              <el-input
                v-model="formData.unit_type"
                placeholder="请输入单元类型，如：优先保护单元3"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="空间布局约束">
          <el-input
            v-model="formData.spatial_constraints"
            type="textarea"
            :rows="3"
            placeholder="请输入空间布局约束"
          />
        </el-form-item>
        
        <el-form-item label="污染物排放管控">
          <el-input
            v-model="formData.pollution_control"
            type="textarea"
            :rows="3"
            placeholder="请输入污染物排放管控要求"
          />
        </el-form-item>
        
        <el-form-item label="环境风险防控">
          <el-input
            v-model="formData.risk_prevention"
            type="textarea"
            :rows="3"
            placeholder="请输入环境风险防控要求"
          />
        </el-form-item>
        
        <el-form-item label="资源开发效率要求">
          <el-input
            v-model="formData.resource_efficiency"
            type="textarea"
            :rows="3"
            placeholder="请输入资源开发效率要求"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="管控单元详情"
      width="800px"
    >
      <div v-if="viewData" class="view-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="管控单元编码">{{ viewData.unit_code }}</el-descriptions-item>
          <el-descriptions-item label="管控单元名称">{{ viewData.unit_name }}</el-descriptions-item>
          <el-descriptions-item label="所属区域">{{ viewData.region_name }}</el-descriptions-item>
          <el-descriptions-item label="所属园区">{{ viewData.park_name || '无' }}</el-descriptions-item>
          <el-descriptions-item label="单元类型" :span="2">
            <el-tag :type="getUnitTypeTag(viewData.unit_type)">{{ viewData.unit_type }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="detail-section">
          <h4>空间布局约束</h4>
          <div class="detail-content">{{ viewData.spatial_constraints || '无' }}</div>
        </div>
        
        <div class="detail-section">
          <h4>污染物排放管控</h4>
          <div class="detail-content">{{ viewData.pollution_control || '无' }}</div>
        </div>
        
        <div class="detail-section">
          <h4>环境风险防控</h4>
          <div class="detail-content">{{ viewData.risk_prevention || '无' }}</div>
        </div>
        
        <div class="detail-section">
          <h4>资源开发效率要求</h4>
          <div class="detail-content">{{ viewData.resource_efficiency || '无' }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, View, Edit, Delete } from '@element-plus/icons-vue'
import { adminApiService } from '@/utils/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const regions = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  search: '',
  region_id: '',
  unit_type: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentId = ref(null)

// 表单数据
const formData = reactive({
  unit_code: '',
  unit_name: '',
  region_id: '',
  park_id: null,
  unit_type: '',
  spatial_constraints: '',
  pollution_control: '',
  risk_prevention: '',
  resource_efficiency: ''
})

// 查看数据
const viewData = ref(null)

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  unit_code: [
    { required: true, message: '请输入管控单元编码', trigger: 'blur' }
  ],
  unit_name: [
    { required: true, message: '请输入管控单元名称', trigger: 'blur' }
  ],
  region_id: [
    { required: true, message: '请选择所属区域', trigger: 'change' }
  ],
  unit_type: [
    { required: true, message: '请输入单元类型', trigger: 'blur' }
  ]
}

// 获取单元类型标签样式
const getUnitTypeTag = (type) => {
  switch (type) {
    case '优先保护单元':
      return 'success'
    case '重点管控单元':
      return 'danger'
    case '一般管控单元':
      return 'warning'
    default:
      return ''
  }
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    const response = await adminApiService.getControlUnits(params)
    tableData.value = response.data.data
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取区域列表
const fetchRegions = async () => {
  try {
    const response = await adminApiService.getRegions()
    regions.value = response.data
  } catch (error) {
    ElMessage.error('获取区域列表失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    region_id: '',
    unit_type: ''
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 选择处理
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 显示新增对话框
const showCreateDialog = () => {
  dialogTitle.value = '新增管控单元'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑管控单元'
  isEdit.value = true
  currentId.value = row.id
  Object.assign(formData, {
    unit_code: row.unit_code,
    unit_name: row.unit_name,
    region_id: row.region_id,
    park_id: row.park_id,
    unit_type: row.unit_type,
    spatial_constraints: row.spatial_constraints,
    pollution_control: row.pollution_control,
    risk_prevention: row.risk_prevention,
    resource_efficiency: row.resource_efficiency
  })
  dialogVisible.value = true
}

// 显示查看对话框
const showViewDialog = (row) => {
  viewData.value = row
  viewDialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    unit_code: '',
    unit_name: '',
    region_id: '',
    park_id: null,
    unit_type: '',
    spatial_constraints: '',
    pollution_control: '',
    risk_prevention: '',
    resource_efficiency: ''
  })
}

// 关闭对话框
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const data = { ...formData, policy_id: 1 } // 假设policy_id为1
        
        if (isEdit.value) {
          await adminApiService.updateControlUnit(currentId.value, data)
          ElMessage.success('更新成功')
        } else {
          await adminApiService.createControlUnit(data)
          ElMessage.success('创建成功')
        }
        
        dialogVisible.value = false
        fetchData()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除管控单元"${row.unit_name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await adminApiService.deleteControlUnit(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 初始化
onMounted(() => {
  fetchData()
  fetchRegions()
})
</script>

<style scoped>
.control-units {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.view-content {
  padding: 10px 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.detail-content {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}
</style>
