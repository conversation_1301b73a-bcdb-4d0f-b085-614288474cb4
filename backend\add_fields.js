const { initDatabase, getDatabase } = require('./config/database');

async function addFields() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 检查并添加数据库字段 ===');
    
    // 检查表结构
    const columns = await new Promise((resolve, reject) => {
      db.all('PRAGMA table_info(focus_points)', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('当前字段:', columns.map(c => c.name).join(', '));
    
    const hasPointType = columns.some(col => col.name === 'point_type');
    const hasIndustryId = columns.some(col => col.name === 'industry_id');
    
    // 添加 point_type 字段
    if (!hasPointType) {
      console.log('添加 point_type 字段...');
      await new Promise((resolve, reject) => {
        db.run('ALTER TABLE focus_points ADD COLUMN point_type VARCHAR(20) DEFAULT "general"', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      console.log('point_type 字段添加成功');
    } else {
      console.log('point_type 字段已存在');
    }
    
    // 添加 industry_id 字段
    if (!hasIndustryId) {
      console.log('添加 industry_id 字段...');
      await new Promise((resolve, reject) => {
        db.run('ALTER TABLE focus_points ADD COLUMN industry_id INTEGER', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      console.log('industry_id 字段添加成功');
    } else {
      console.log('industry_id 字段已存在');
    }
    
    // 检查现有数据
    const result = await new Promise((resolve, reject) => {
      db.all('SELECT COUNT(*) as count FROM focus_points', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('现有关注点数量:', result[0].count);
    
    // 如果没有数据，初始化一些测试数据
    if (result[0].count === 0) {
      console.log('初始化测试数据...');
      
      const testData = [
        { name: '环评', description: '环境影响评价相关要求', category: '通用', keywords: '环评,环境影响评价', point_type: 'general', sort_order: 1 },
        { name: '节能评估', description: '节能评估相关要求', category: '通用', keywords: '节能,能耗', point_type: 'general', sort_order: 2 },
        { name: '安全评价', description: '安全评价相关要求', category: '通用', keywords: '安全,评价', point_type: 'general', sort_order: 3 },
        { name: 'VOCs排放控制', description: '挥发性有机化合物排放控制要求', category: '废气治理', keywords: 'VOCs,挥发性有机物,废气', point_type: 'industry', sort_order: 1 },
        { name: '重金属污染防治', description: '重金属污染防治相关要求', category: '污染防治', keywords: '重金属,污染防治,土壤', point_type: 'industry', sort_order: 2 },
        { name: '废水处理要求', description: '工业废水处理相关要求', category: '水污染防治', keywords: '废水,污水处理,COD', point_type: 'industry', sort_order: 3 }
      ];
      
      for (const data of testData) {
        await new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO focus_points (name, description, category, keywords, point_type, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [data.name, data.description, data.category, data.keywords, data.point_type, data.sort_order, 1],
            function(err) {
              if (err) reject(err);
              else resolve(this.lastID);
            }
          );
        });
      }
      
      console.log('测试数据初始化完成，共插入', testData.length, '条记录');
    }
    
    console.log('=== 完成 ===');
    process.exit(0);
    
  } catch (error) {
    console.error('操作失败:', error);
    process.exit(1);
  }
}

addFields();
