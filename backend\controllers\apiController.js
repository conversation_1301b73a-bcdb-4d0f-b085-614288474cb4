const Region = require('../models/Region');
const Park = require('../models/Park');
const ControlUnit = require('../models/ControlUnit');
const Industry = require('../models/Industry');
const IndustryClassification = require('../models/IndustryClassification');
const FocusPoint = require('../models/FocusPoint');
const GeneralFocusPoint = require('../models/GeneralFocusPoint');
const IndustryFocusPoint = require('../models/IndustryFocusPoint');
const { getDatabase } = require('../config/database');

class ApiController {
  // 获取区域列表
  static async getRegions(req, res) {
    try {
      const regions = await Region.getAll();
      res.json({ code: 200, data: regions });
    } catch (error) {
      console.error('获取区域列表失败:', error);
      res.status(500).json({ code: 500, message: '获取区域列表失败', error: error.message });
    }
  }

  // 获取园区列表（根据地区筛选）
  static async getParks(req, res) {
    try {
      const { region_id } = req.query;
      const parks = await Park.getAll(region_id);
      res.json({ code: 200, data: parks });
    } catch (error) {
      console.error('获取园区列表失败:', error);
      res.status(500).json({ code: 500, message: '获取园区列表失败', error: error.message });
    }
  }

  // 获取单个园区信息
  static async getParkById(req, res) {
    try {
      const { id } = req.params;
      const park = await Park.getById(id);
      if (!park) {
        return res.status(404).json({ code: 404, message: '园区不存在' });
      }
      res.json({ code: 200, data: park });
    } catch (error) {
      console.error('获取园区信息失败:', error);
      res.status(500).json({ code: 500, message: '获取园区信息失败', error: error.message });
    }
  }

  // 获取管控单元列表（根据地区筛选，排除园区）
  static async getControlUnits(req, res) {
    try {
      const { region_id } = req.query;
      
      if (!region_id) {
        return res.status(400).json({ code: 400, message: '缺少region_id参数' });
      }
      
      const controlUnits = await ControlUnit.getByRegion(region_id);
      res.json({ code: 200, data: controlUnits });
    } catch (error) {
      console.error('获取管控单元失败:', error);
      res.status(500).json({ code: 500, message: '获取管控单元失败', error: error.message });
    }
  }

  // 获取行业列表（基于分类管理名录）
  static async getIndustries(req, res) {
    try {
      const result = await Industry.getCascadeData();
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取行业列表失败:', error);
      res.status(500).json({ code: 500, message: '获取行业列表失败', error: error.message });
    }
  }

  // 获取特定行业ID的环境影响评价要求
  static async getIndustryRequirements(req, res) {
    try {
      const { industryId } = req.params;
      const result = await Industry.getRequirements(industryId);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取行业要求失败:', error);
      res.status(500).json({ code: 500, message: '获取行业要求失败', error: error.message });
    }
  }

  // 新的、统一的国民经济行业分类详情接口
  static async getIndustryClassificationDetail(req, res) {
    try {
      const { name } = req.query;
      if (!name) {
        return res.status(400).json({ code: 400, message: '行业名称不能为空' });
      }

      const result = await IndustryClassification.getDetail(name);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('查找行业分类详情失败:', error);
      if (error.message.includes('未找到')) {
        res.status(404).json({ code: 404, message: error.message });
      } else {
        res.status(500).json({ code: 500, message: '查找行业分类详情失败', error: error.message });
      }
    }
  }

  // 搜索国民经济行业分类
  static async searchIndustryClassification(req, res) {
    try {
      const { keyword } = req.query;
      const result = await IndustryClassification.search(keyword);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('搜索行业分类失败:', error);
      res.status(500).json({ code: 500, message: '搜索行业分类失败', error: error.message });
    }
  }

  // 获取行业关键词
  static async getIndustryKeywords(req, res) {
    try {
      const { industry_id } = req.query;
      
      if (!industry_id) {
        return res.status(400).json({ code: 400, message: '缺少industry_id参数' });
      }
      
      const db = getDatabase();
      db.all("SELECT keyword FROM industry_keywords WHERE industry_id = ?", [industry_id], (err, rows) => {
        if (err) {
          console.error('获取行业关键词失败:', err);
          res.status(500).json({ code: 500, message: '获取行业关键词失败', error: err.message });
        } else {
          res.json({ code: 200, data: rows.map(row => row.keyword) });
        }
      });
    } catch (error) {
      console.error('获取行业关键词失败:', error);
      res.status(500).json({ code: 500, message: '获取行业关键词失败', error: error.message });
    }
  }

  // 获取通用标签
  static async getGeneralTags(req, res) {
    try {
      const db = getDatabase();
      db.all("SELECT * FROM general_tags ORDER BY tag_name", (err, rows) => {
        if (err) {
          console.error('获取通用标签失败:', err);
          res.status(500).json({ code: 500, message: '获取通用标签失败', error: err.message });
        } else {
          res.json({ code: 200, data: rows });
        }
      });
    } catch (error) {
      console.error('获取通用标签失败:', error);
      res.status(500).json({ code: 500, message: '获取通用标签失败', error: error.message });
    }
  }

  // 根据行业ID获取相关的行业关注点（支持层级匹配）
  static async getIndustryFocusPointsByIndustry(req, res) {
    try {
      const { industry_id } = req.query;

      if (!industry_id) {
        return res.status(400).json({ code: 400, message: '行业ID不能为空' });
      }

      // 获取行业分类信息
      const classificationInfo = await IndustryClassification.getClassificationInfo(industry_id);

      if (!classificationInfo) {
        return res.status(404).json({ code: 404, message: '未找到行业分类信息' });
      }

      // 获取匹配的行业关注点
      const focusPoints = await IndustryFocusPoint.getByClassificationInfo(classificationInfo);

      res.json({ code: 200, data: focusPoints });
    } catch (error) {
      console.error('获取行业关注点失败:', error);
      res.status(500).json({ code: 500, message: '获取行业关注点失败', error: error.message });
    }
  }

  // 必选项查询（精准匹配）
  static async searchRequired(req, res) {
    try {
      const { region_id, in_park, park_id, control_unit_id, industry_id } = req.query;
      
      if (!region_id || in_park === undefined || !industry_id) {
        return res.status(400).json({ code: 400, message: '缺少必要参数' });
      }
      
      const isInPark = in_park === 'true';
      
      if (isInPark && !park_id) {
        return res.status(400).json({ code: 400, message: '选择在园区时必须提供park_id' });
      }
      
      if (!isInPark && !control_unit_id) {
        return res.status(400).json({ code: 400, message: '选择不在园区时必须提供control_unit_id' });
      }
      
      const result = {
        sanxianyidan: [],
        industry_policies: [],
        park_policies: []
      };
      
      const db = getDatabase();
      
      // 查询三线一单管控要求
      if (isInPark) {
        // 园区管控单元
        const sql = `
          SELECT * FROM control_units 
          WHERE park_id = ? AND region_id = ?
        `;
        const rows = await new Promise((resolve, reject) => {
          db.all(sql, [park_id, region_id], (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        });
        result.sanxianyidan = rows;
      } else {
        // 非园区管控单元
        const sql = `
          SELECT * FROM control_units 
          WHERE id = ?
        `;
        const row = await new Promise((resolve, reject) => {
          db.get(sql, [control_unit_id], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });
        if (row) result.sanxianyidan = [row];
      }
      
      // 查询园区政策（从园区要求表获取）
      if (isInPark && park_id) {
        const parkRequirements = await Park.getRequirements(park_id);
        result.park_policies = parkRequirements;
      }
      
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('必选项查询失败:', error);
      res.status(500).json({ code: 500, message: '必选项查询失败', error: error.message });
    }
  }

  // 可选项查询（基于关注点ID）
  static async searchOptional(req, res) {
    try {
      const { industry_keywords, general_tags } = req.query;

      let allFocusPoints = [];

      // 处理通用关注点
      if (general_tags) {
        const generalIds = Array.isArray(general_tags) ? general_tags : [general_tags];
        const generalPoints = await GeneralFocusPoint.getByIds(generalIds);
        allFocusPoints = allFocusPoints.concat(generalPoints);
      }

      // 处理行业关注点
      if (industry_keywords) {
        const industryIds = Array.isArray(industry_keywords) ? industry_keywords : [industry_keywords];
        const industryPoints = await IndustryFocusPoint.getByIds(industryIds);
        allFocusPoints = allFocusPoints.concat(industryPoints);
      }

      res.json({ code: 200, data: allFocusPoints });
    } catch (error) {
      console.error('可选项查询失败:', error);
      res.status(500).json({ code: 500, message: '可选项查询失败', error: error.message });
    }
  }

  // 三线一单查询
  static async searchSanxianyidan(req, res) {
    try {
      const { location, unitType } = req.body;
      
      if (!location) {
        return res.status(400).json({ code: 400, message: '请输入项目位置' });
      }
      
      const result = await ControlUnit.search(location, unitType);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('三线一单查询失败:', error);
      res.status(500).json({ code: 500, message: '查询失败', error: error.message });
    }
  }
}

module.exports = ApiController;
