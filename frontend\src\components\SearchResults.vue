<template>
  <section v-if="hasResults" class="results-section">
    <el-tabs type="border-card" class="main-tabs">
      <!-- 必须关注 -->
      <el-tab-pane v-if="hasRequiredResults">
        <template #label>
          <span class="tab-label">
            <el-icon><WarningFilled /></el-icon> 必须关注
          </span>
        </template>
        
        <el-tabs v-model="activeRequiredTab" type="card" class="content-tabs">
          <!-- 分类管理名录 -->
          <el-tab-pane label="分类管理名录" name="requirements" v-if="industryRequirements">
            <IndustryRequirementsTable :industryRequirements="industryRequirements" />
          </el-tab-pane>
          
          <!-- 国民经济行业 -->
          <el-tab-pane label="国民经济行业" name="classification" v-if="industryInfo.name">
            <IndustryClassificationDetail :industry-info="industryInfo" />
          </el-tab-pane>

          <!-- 三线一单 -->
          <el-tab-pane label="三线一单" name="sanxianyidan" v-if="requiredResults.sanxianyidan && requiredResults.sanxianyidan.length > 0">
            <SanxianyidanResult :units="requiredResults.sanxianyidan" />
          </el-tab-pane>

          <!-- 开发区要求 -->
          <el-tab-pane label="开发区要求" name="park_policies" v-if="requiredResults.park_policies && requiredResults.park_policies.length > 0">
            <div class="park-policies-container">
              <div class="policies-header">
                <h3 class="section-title">
                  <el-icon class="title-icon"><Document /></el-icon>
                  园区管理要求
                </h3>
                <div class="policies-count">
                  共 {{ requiredResults.park_policies.length }} 项要求
                </div>
              </div>

              <!-- 卡片列表 -->
              <div class="policies-grid">
                <div
                  v-for="(policy, index) in parkPoliciesPage"
                  :key="policy.id || index"
                  class="policy-card"
                >
                  <div class="card-header">
                    <div class="card-number">{{ (currentPage - 1) * pageSize + index + 1 }}</div>
                    <el-tag
                      :type="getCategoryTagType(policy.category)"
                      class="category-tag"
                      effect="light"
                      size="large"
                    >
                      <el-icon class="tag-icon"><Star /></el-icon>
                      {{ policy.category || '未分类' }}
                    </el-tag>
                  </div>

                  <div class="card-body">
                    <h4 class="policy-title">{{ policy.requirement_title }}</h4>
                    <div class="policy-content">
                      {{ policy.requirement_content }}
                    </div>
                  </div>

                  <div class="card-footer">
                    <div class="policy-meta">
                      <span class="meta-item">
                        <el-icon><Clock /></el-icon>
                        {{ formatDate(policy.created_at) }}
                      </span>
                      <span class="meta-item" v-if="policy.updated_at !== policy.created_at">
                        <el-icon><Edit /></el-icon>
                        {{ formatDate(policy.updated_at) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 开发区相关文件 -->
              <div class="park-files-section" v-if="parkFiles && parkFiles.length > 0">
                <div class="files-header">
                  <h4 class="files-title">
                    <el-icon class="title-icon"><FolderOpened /></el-icon>
                    开发区相关文件
                  </h4>
                </div>
                <div class="files-grid">
                  <div
                    v-for="(file, index) in parkFiles"
                    :key="index"
                    class="file-card"
                    @click="openFile(file)"
                  >
                    <div class="file-icon">
                      <el-icon><Document /></el-icon>
                    </div>
                    <div class="file-info">
                      <div class="file-name">{{ file.name }}</div>
                      <div class="file-url">{{ file.url }}</div>
                    </div>
                    <div class="file-action">
                      <el-icon><View /></el-icon>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination-container" v-if="requiredResults.park_policies.length > pageSize">
                <el-pagination
                  v-model:current-page="currentPage"
                  :page-size="pageSize"
                  layout="total, prev, pager, next, jumper"
                  :total="requiredResults.park_policies.length"
                  @current-change="handlePageChange"
                  background
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>

      <!-- 可能需要关注 -->
      <el-tab-pane v-if="selectedFocusPoints.length > 0">
        <template #label>
          <span class="tab-label">
            <el-icon><InfoFilled /></el-icon> 可能需要关注
          </span>
        </template>

        <!-- 显示选中的关注点 -->
        <div class="focus-points-section">
          <div class="focus-points-header">
            <h3 class="section-title">
              <el-icon class="title-icon"><Star /></el-icon>
              您关注的要点
            </h3>
            <div class="focus-points-count">
              共 {{ selectedFocusPoints.length }} 个关注点
            </div>
          </div>
          <div class="focus-points-grid">
            <div
              v-for="(point, index) in selectedFocusPoints"
              :key="point.id"
              class="focus-point-card"
            >
              <div class="focus-point-header">
                <div class="focus-point-number">{{ index + 1 }}</div>
                <div class="focus-point-main">
                  <h4 class="focus-point-name">{{ point.name }}</h4>
                  <el-tag
                    :type="point.industry_names ? 'success' : 'primary'"
                    size="small"
                    class="focus-point-type"
                  >
                    {{ point.industry_names ? '行业' : '通用' }}
                  </el-tag>
                </div>
              </div>
              <div v-if="point.description" class="focus-point-description">
                {{ point.description }}
              </div>
              <div class="focus-point-tags">
                <div v-if="point.keywords" class="focus-point-keywords">
                  <span class="keywords-label">关键词</span>
                  <div class="keywords-tags">
                    <el-tag
                      v-for="keyword in point.keywords.split(',')"
                      :key="keyword.trim()"
                      size="small"
                      type="primary"
                    >
                      {{ keyword.trim() }}
                    </el-tag>
                  </div>
                </div>
                <div v-if="point.industry_names" class="focus-point-industries">
                  <span class="industries-label">行业</span>
                  <div class="industries-tags">
                    <el-tag
                      v-for="industry in point.industry_names.split(',')"
                      :key="industry.trim()"
                      size="small"
                      type="warning"
                    >
                      {{ industry.trim() }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 统一的政策文件区域 -->
          <div v-if="getAllPolicyFiles().length > 0" class="unified-policy-section">
            <h3 class="section-title">
              <el-icon><Document /></el-icon>
              相关政策文件
            </h3>
            <div class="policy-files-grid">
              <div
                v-for="(file, index) in getAllPolicyFiles()"
                :key="index"
                class="policy-file-card"
              >
                <div class="policy-file-header">
                  <el-icon class="file-icon"><Document /></el-icon>
                  <h4 class="file-name">{{ file.name }}</h4>
                </div>
                <div v-if="file.source" class="file-source">
                  来源：{{ file.source }}
                </div>
                <div class="file-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="openPolicyFile(file.url)"
                  >
                    <el-icon><View /></el-icon>
                    查看文件
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 无结果提示 -->
    <div v-if="!hasRequiredResults && optionalResults.length === 0" class="section-card empty-card">
      <div class="empty-content">
        <i class="icon-empty"></i>
        <h3>未找到相关政策信息</h3>
        <p>请调整筛选条件后重新查询</p>
      </div>
    </div>
  </section>
</template>

<script>
import { ref, computed, watch } from 'vue'
import SanxianyidanResult from './SanxianyidanResult.vue'
import IndustryRequirementsTable from './IndustryRequirementsTable.vue'
import IndustryClassificationDetail from './IndustryClassificationDetail.vue'
import PolicyResultItem from './PolicyResultItem.vue'
import { ElTabs, ElTabPane, ElIcon, ElTag, ElButton, ElTable, ElTableColumn, ElPagination } from 'element-plus'
import { WarningFilled, InfoFilled, Star, Document, View, Clock, Edit, FolderOpened } from '@element-plus/icons-vue'

export default {
  name: 'SearchResults',
  components: {
    SanxianyidanResult,
    IndustryRequirementsTable,
    IndustryClassificationDetail,
    PolicyResultItem,
    ElTabs,
    ElTabPane,
    ElIcon,
    ElTag,
    ElButton,
    ElTable,
    ElTableColumn,
    ElPagination,
    WarningFilled,
    InfoFilled,
    Star,
    Document,
    View,
    Clock,
    Edit,
    FolderOpened,
  },
  props: {
    requiredResults: {
      type: Object,
      default: () => ({ sanxianyidan: [], industry_policies: [], park_policies: [] })
    },
    optionalResults: {
      type: Array,
      default: () => []
    },
    industryRequirements: {
      type: Object,
      default: null
    },
    industryInfo: {
      type: Object,
      default: () => ({ code: '', name: '' })
    },
    hasResults: {
      type: Boolean,
      default: false
    },
    selectedFocusPoints: {
      type: Array,
      default: () => []
    },
    parkFiles: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const activeRequiredTab = ref('requirements');
    const currentPage = ref(1);
    const pageSize = ref(10);

    const hasRequiredResults = computed(() => {
      return (props.requiredResults.sanxianyidan && props.requiredResults.sanxianyidan.length > 0) ||
             props.industryRequirements ||
             (props.industryInfo && props.industryInfo.name) ||
             (props.requiredResults.park_policies && props.requiredResults.park_policies.length > 0)
    });

    // 开发区要求分页数据
    const parkPoliciesPage = computed(() => {
      console.log('parkPoliciesPage computed:', props.requiredResults.park_policies);
      if (!props.requiredResults.park_policies) return [];
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return props.requiredResults.park_policies.slice(start, end);
    });

    // 智能切换默认显示的子Tab
    watch(() => [props.industryRequirements, props.industryInfo, props.requiredResults.park_policies], () => {
        if (props.industryRequirements) {
            activeRequiredTab.value = 'requirements';
        } else if (props.industryInfo.name) {
            activeRequiredTab.value = 'classification';
        } else if (props.requiredResults.park_policies && props.requiredResults.park_policies.length > 0) {
            activeRequiredTab.value = 'park_policies';
        } else if (props.requiredResults.sanxianyidan && props.requiredResults.sanxianyidan.length > 0) {
            activeRequiredTab.value = 'sanxianyidan';
        }
    }, { immediate: true });

    // 分页处理
    const handlePageChange = (page) => {
      currentPage.value = page;
    };

    // 获取分类标签类型
    const getCategoryTagType = (category) => {
      const typeMap = {
        '准入要求': 'danger',
        '环保要求': 'success',
        '安全要求': 'warning',
        '规划要求': 'warning',
        '产业政策': 'primary',
        '投资强度': 'warning',
        '税收要求': 'success',
        '其他要求': 'warning',
        '限制要求': 'danger',
        '禁止要求': 'danger',
        '鼓励要求': 'success',
        '优先要求': 'primary'
      };
      return typeMap[category] || 'primary';
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      } catch (error) {
        return '';
      }
    };

    // 打开文件
    const openFile = (file) => {
      if (file.url) {
        window.open(file.url, '_blank');
      }
    };

    // 获取政策文件列表
    const getPolicyFiles = (point) => {
      if (!point.policy_files) return []
      try {
        return JSON.parse(point.policy_files)
      } catch (error) {
        console.error('解析政策文件失败:', error)
        return []
      }
    }

    // 获取所有关注点的政策文件（去重）
    const getAllPolicyFiles = () => {
      const allFiles = [];
      const fileMap = new Map(); // 用于去重

      props.selectedFocusPoints.forEach(point => {
        const files = getPolicyFiles(point);
        files.forEach(file => {
          const key = `${file.name}-${file.url}`;
          if (!fileMap.has(key)) {
            fileMap.set(key, {
              ...file,
              source: point.name // 添加来源信息
            });
            allFiles.push(fileMap.get(key));
          }
        });
      });

      return allFiles;
    };

    // 打开政策文件
    const openPolicyFile = (url) => {
      if (url) {
        window.open(url, '_blank');
      }
    };

    return {
      hasRequiredResults,
      activeRequiredTab,
      currentPage,
      pageSize,
      parkPoliciesPage,
      handlePageChange,
      getCategoryTagType,
      formatDate,
      openFile,
      getPolicyFiles,
      getAllPolicyFiles,
      openPolicyFile
    }
  }
}
</script>

<style scoped>
/* Base Styles */
.results-section {
  margin-top: 20px;
}
.main-tabs {
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color, #e5e9f2);
  overflow: hidden;
  background-color: var(--card-bg-color, rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
}
.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
}
.tab-label .el-icon {
  font-size: 1.2em;
}

:deep(.el-tabs__header) {
    background-color: rgba(255,255,255,0.5);
    border-bottom: 1px solid var(--border-color, #e5e9f2);
}

.content-tabs {
  border: none;
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
    border: none;
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
    border-radius: 8px 8px 0 0;
    border: 1px solid transparent;
    border-bottom: none;
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    border-color: var(--border-color, #e5e9f2);
    background-color: #fff;
}


.optional-results-container {
  padding: 15px;
}

/* 关注点样式 - 与开发区页面保持一致 */
.focus-points-section {
  padding: 20px 0;
}

.focus-points-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.focus-points-count {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.focus-points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.focus-point-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  overflow: hidden;
}

.focus-point-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.focus-point-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.focus-point-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.focus-point-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  gap: 12px;
}

.focus-point-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

.focus-point-type {
  font-weight: 500;
  border-radius: 8px;
  padding: 6px 14px;
  flex-shrink: 0;
}

.focus-point-description {
  color: #4b5563;
  line-height: 1.6;
  font-size: 14px;
  text-align: justify;
  word-break: break-word;
  padding: 0 20px 12px;
}

.focus-point-tags {
  padding: 12px 20px;
  background: #f9fafb;
  border-top: 1px solid #f3f4f6;
}

.focus-point-keywords,
.focus-point-industries {
  margin-bottom: 12px;
}

.focus-point-keywords:last-child,
.focus-point-industries:last-child {
  margin-bottom: 0;
}

.keywords-label,
.industries-label {
  display: inline-block;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 6px;
  margin-right: 8px;
}

.keywords-tags,
.industries-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.keywords-tags .el-tag,
.industries-tags .el-tag {
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 10px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.focus-points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
}

.focus-point-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.focus-point-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.focus-point-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.focus-point-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  margin-right: 12px;
}

.focus-point-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
}

.focus-point-keywords,
.focus-point-industries,
.focus-point-policies {
  margin-bottom: 8px;
}

.keywords-label,
.industries-label,
.policies-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.policy-files-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 4px;
}

.policy-file-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  text-decoration: none;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
  max-width: fit-content;
}

.policy-file-link:hover {
  background-color: #ecf5ff;
  color: #337ecc;
}

.policy-file-link .el-icon {
  font-size: 14px;
}

@media (max-width: 768px) {
  .focus-points-grid {
    grid-template-columns: 1fr;
  }

  .focus-point-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .focus-point-name {
    margin-right: 0;
  }
}

/* Empty State */
.empty-card {
  text-align: center;
  padding: 60px 30px;
  background: var(--card-bg-color, rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color, #e5e9f2);
}

.empty-content i {
  font-size: 4rem;
  margin-bottom: 20px;
  display: block;
}

.empty-content h3 {
  color: #374151;
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.empty-content p {
  color: #6b7280;
  margin: 0;
}

/* 统一的政策文件区域 */
.unified-policy-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.policy-files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.policy-file-card {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.policy-file-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.policy-file-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.file-icon {
  color: #409eff;
  font-size: 18px;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  flex: 1;
}

.file-source {
  font-size: 12px;
  color: #909399;
  margin-bottom: 12px;
}

.file-actions {
  display: flex;
  justify-content: flex-end;
}

/* 开发区要求样式 */
.park-policies-container {
  padding: 20px 0;
}

.policies-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.title-icon {
  margin-right: 8px;
  color: #3b82f6;
}

.policies-count {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.policies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.policy-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  overflow: hidden;
}

.policy-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.card-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.category-tag {
  font-weight: 500;
  border-radius: 8px;
  padding: 6px 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag-icon {
  font-size: 12px;
}

.card-body {
  padding: 20px;
}

.policy-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.policy-content {
  color: #4b5563;
  line-height: 1.6;
  font-size: 14px;
  text-align: justify;
  word-break: break-word;
}

.card-footer {
  padding: 12px 20px;
  background: #f9fafb;
  border-top: 1px solid #f3f4f6;
}

.policy-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-item .el-icon {
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

/* 开发区文件样式 */
.park-files-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 2px solid #f0f2f5;
}

.files-header {
  margin-bottom: 20px;
}

.files-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.file-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.file-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.file-icon .el-icon {
  color: white;
  font-size: 18px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-url {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-action {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  flex-shrink: 0;
}

.file-card:hover .file-action {
  color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  /* 开发区要求移动端优化 - 充分利用空间 */
  .park-policies-container {
    padding: 8px 4px;
  }

  .policies-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 8px;
    gap: 8px;
  }

  .section-title {
    font-size: 16px;
    margin: 0;
    flex: 1;
    min-width: 0;
  }

  .title-icon {
    margin-right: 4px;
  }

  .policies-count {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 12px;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .policies-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 16px;
    padding: 0 4px;
  }

  .policy-card {
    margin: 0;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .card-header {
    padding: 8px 12px 6px;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    background: #f8fafc;
  }

  .card-number {
    width: 20px;
    height: 20px;
    font-size: 10px;
    flex-shrink: 0;
    order: 2;
  }

  .category-tag {
    padding: 2px 6px;
    font-size: 10px;
    flex-shrink: 0;
    order: 1;
  }

  .tag-icon {
    font-size: 8px;
  }

  .card-body {
    padding: 8px 12px;
  }

  .policy-title {
    font-size: 14px;
    margin-bottom: 6px;
    line-height: 1.3;
    font-weight: 600;
  }

  .policy-content {
    font-size: 12px;
    line-height: 1.4;
    color: #4b5563;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .card-footer {
    padding: 6px 12px;
    background: #f9fafb;
    border-top: 1px solid #f3f4f6;
  }

  .policy-meta {
    flex-direction: row;
    justify-content: space-between;
    gap: 8px;
    font-size: 10px;
  }

  .meta-item {
    flex: 1;
    min-width: 0;
  }

  .meta-item .el-icon {
    font-size: 10px;
  }

  /* 开发区文件移动端优化 - 充分利用空间 */
  .park-files-section {
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #e2e8f0;
  }

  .files-header {
    margin-bottom: 8px;
    padding: 0 8px;
  }

  .files-title {
    font-size: 14px;
    margin: 0;
  }

  .files-grid {
    grid-template-columns: 1fr;
    gap: 6px;
    padding: 0 4px;
  }

  .file-card {
    padding: 8px 12px;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    background: #fafafa;
  }

  .file-card:hover {
    transform: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .file-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
  }

  .file-icon .el-icon {
    font-size: 14px;
  }

  .file-info {
    flex: 1;
    min-width: 0;
    text-align: left;
  }

  .file-name {
    font-size: 12px;
    margin-bottom: 2px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .file-url {
    font-size: 10px;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .file-action {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
  }

  .file-action .el-icon {
    font-size: 12px;
  }
}

/* 可能需要关注移动端优化 */
@media (max-width: 768px) {
  /* Tab标签优化 - 解决四个标签显示问题 */
  :deep(.el-tabs__nav-wrap) {
    padding: 0 4px;
  }

  :deep(.el-tabs__nav-scroll) {
    overflow: visible;
  }

  :deep(.el-tabs__nav) {
    display: flex;
    width: 100%;
  }

  :deep(.el-tabs__item) {
    flex: 1;
    padding: 0 2px;
    font-size: 11px;
    text-align: center;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
  }

  :deep(.el-tabs__nav-prev),
  :deep(.el-tabs__nav-next) {
    display: none !important;
  }

  /* 关注点网格优化 - 充分利用空间，与开发区页面一致 */
  .focus-points-section {
    padding: 8px 4px;
  }

  .focus-points-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 8px;
    gap: 8px;
  }

  .focus-points-count {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 12px;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .focus-points-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 16px;
    padding: 0 4px;
  }

  .focus-point-card {
    margin: 0;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .focus-point-header {
    padding: 8px 12px 6px;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    background: #f8fafc;
  }

  .focus-point-number {
    width: 20px;
    height: 20px;
    font-size: 10px;
    flex-shrink: 0;
    order: 2;
  }

  .focus-point-main {
    flex: 1;
    order: 1;
    gap: 6px;
  }

  .focus-point-name {
    font-size: 14px;
    line-height: 1.3;
    margin: 0;
    font-weight: 600;
  }

  .focus-point-type {
    padding: 2px 6px;
    font-size: 10px;
    margin-top: 4px;
    align-self: flex-start;
  }

  .focus-point-description {
    font-size: 12px;
    line-height: 1.4;
    color: #4b5563;
    padding: 0 12px 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .focus-point-tags {
    padding: 6px 12px;
    background: #f9fafb;
    border-top: 1px solid #f3f4f6;
  }

  .focus-point-keywords,
  .focus-point-industries {
    margin-bottom: 6px;
  }

  .focus-point-keywords:last-child,
  .focus-point-industries:last-child {
    margin-bottom: 0;
  }

  .keywords-label,
  .industries-label {
    font-size: 10px;
    margin-bottom: 3px;
    margin-right: 4px;
  }

  .keywords-tags,
  .industries-tags {
    gap: 3px;
  }

  .keywords-tags .el-tag,
  .industries-tags .el-tag {
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 3px;
    line-height: 1.2;
  }

  /* 政策结果项移动端优化 - 充分利用空间 */
  .policy-result-item {
    margin-bottom: 8px;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
  }

  .policy-result-header {
    padding: 8px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    background: #f8fafc;
  }

  .policy-result-title {
    font-size: 14px;
    line-height: 1.3;
    font-weight: 600;
    margin: 0;
    width: 100%;
  }

  .policy-result-tags {
    flex-wrap: wrap;
    gap: 3px;
    width: 100%;
  }

  .policy-result-tags .el-tag {
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 3px;
    line-height: 1.2;
  }

  .policy-result-content {
    padding: 8px 12px;
    font-size: 12px;
    line-height: 1.4;
    color: #4b5563;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 空状态优化 - 充分利用空间 */
  .empty-card {
    padding: 20px 12px;
    margin: 0 4px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    background: #f9fafb;
  }

  .empty-card h3 {
    font-size: 14px;
    margin-bottom: 6px;
    color: #374151;
  }

  .empty-card p {
    font-size: 12px;
    line-height: 1.4;
    color: #6b7280;
    margin: 0;
  }

  /* 分页优化 - 充分利用空间 */
  .pagination-container {
    margin-top: 16px;
    padding: 0 4px;
  }

  :deep(.el-pagination) {
    justify-content: center;
    flex-wrap: wrap;
  }

  :deep(.el-pagination .el-pager li) {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 11px;
    margin: 0 1px;
  }

  :deep(.el-pagination .btn-prev),
  :deep(.el-pagination .btn-next) {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 11px;
  }

  :deep(.el-pagination .el-pagination__total),
  :deep(.el-pagination .el-pagination__jump) {
    font-size: 11px;
  }
}

/* 超小屏幕优化 (480px以下) */
@media (max-width: 480px) {
  /* 开发区要求超小屏优化 */
  .park-policies-container {
    padding: 8px 0;
  }

  .policies-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .section-title {
    font-size: 16px;
  }

  .policies-count {
    padding: 6px 12px;
    font-size: 12px;
  }

  .policies-grid {
    gap: 8px;
  }

  .policy-card {
    border-radius: 6px;
  }

  .card-header {
    padding: 10px 12px 6px;
  }

  .card-number {
    width: 20px;
    height: 20px;
    font-size: 11px;
  }

  .category-tag {
    padding: 3px 8px;
    font-size: 11px;
  }

  .card-body {
    padding: 8px 12px;
  }

  .policy-title {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .policy-content {
    font-size: 12px;
  }

  .card-footer {
    padding: 6px 12px;
  }

  .policy-meta {
    font-size: 10px;
  }

  /* 文件卡片超小屏优化 */
  .file-card {
    padding: 10px;
  }

  .file-icon {
    width: 28px;
    height: 28px;
  }

  .file-name {
    font-size: 13px;
  }

  .file-url {
    font-size: 10px;
  }

  /* 关注点超小屏优化 */
  .focus-points-grid {
    gap: 8px;
    padding: 0 4px;
  }

  .focus-point-name {
    font-size: 14px;
  }

  .focus-point-description {
    font-size: 12px;
  }

  .keywords-label,
  .industries-label {
    font-size: 11px;
  }

  .focus-point-keywords .el-tag,
  .focus-point-industries .el-tag {
    font-size: 10px;
    padding: 1px 4px;
  }

  /* Tab标签超小屏优化 */
  :deep(.el-tabs__item) {
    padding: 0 6px;
    font-size: 12px;
  }

  /* 分页超小屏优化 */
  :deep(.el-pagination .el-pager li) {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 11px;
  }

  :deep(.el-pagination .btn-prev),
  :deep(.el-pagination .btn-next) {
    min-width: 24px;
    height: 24px;
    line-height: 24px;
  }
}
</style> 