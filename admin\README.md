# 蓝天Wiki后台管理系统设计方案

## 系统架构概述

### 技术栈
- **前端**: Vue 3 + Element Plus + Vue Router
- **后端**: Node.js + Express.js + SQLite
- **认证**: JWT Token
- **权限**: RBAC (基于角色的访问控制)

### 目录结构
```
admin/
├── frontend/                 # 后台前端
│   ├── src/
│   │   ├── views/            # 页面组件
│   │   │   ├── Dashboard.vue # 仪表板
│   │   │   ├── Login.vue     # 登录页
│   │   │   ├── DataManagement/
│   │   │   │   ├── ControlUnits.vue      # 三线一单管理
│   │   │   │   ├── Industries.vue        # 分类管理名录
│   │   │   │   └── Classifications.vue   # 国民经济行业
│   │   │   ├── ParkManagement/
│   │   │   │   └── Parks.vue             # 开发区管理
│   │   │   └── FocusManagement/
│   │   │       ├── Keywords.vue          # 行业关键词
│   │   │       └── GeneralTags.vue       # 通用标签
│   │   ├── components/       # 公共组件
│   │   │   ├── Layout/       # 布局组件
│   │   │   ├── DataTable.vue # 数据表格组件
│   │   │   └── FormDialog.vue# 表单对话框
│   │   ├── router/           # 路由配置
│   │   ├── store/            # 状态管理
│   │   └── utils/            # 工具函数
│   └── package.json
├── backend/                  # 后台API
│   ├── routes/
│   │   ├── auth.js          # 认证路由
│   │   ├── admin/           # 管理员路由
│   │   │   ├── control-units.js
│   │   │   ├── industries.js
│   │   │   ├── classifications.js
│   │   │   ├── parks.js
│   │   │   └── focus.js
│   │   └── index.js
│   ├── middleware/
│   │   ├── auth.js          # 认证中间件
│   │   └── permission.js    # 权限中间件
│   └── models/              # 数据模型
└── README.md
```

## 功能模块设计

### 1. 数据管理模块

#### 1.1 三线一单管理 (`/admin/control-units`)
**功能特点:**
- 分页展示所有管控单元
- 支持按区域、园区、单元类型筛选
- 支持增删改操作
- 四大约束字段的富文本编辑

**数据表结构:**
```sql
control_units (
  id, policy_id, unit_code, unit_name, 
  region_id, park_id, unit_type,
  spatial_constraints,      -- 空间布局约束
  pollution_control,        -- 污染物排放管控  
  risk_prevention,          -- 环境风险防控
  resource_efficiency       -- 资源开发效率要求
)
```

#### 1.2 分类管理名录 (`/admin/industries`)
**功能特点:**
- 分页展示所有行业分类
- 支持按类别、代码搜索
- 环评要求字段编辑（报告书/报告表/登记表）
- 批量导入功能

**数据表结构:**
```sql
industries (
  id, code, name, category, category_order, item_number,
  report_book, report_table, registration_form, 
  sensitive_area_meaning
)
```

#### 1.3 国民经济行业分类 (`/admin/classifications`)
**功能特点:**
- 树形结构展示（支持展开/折叠）
- 按层级分页（1级门类、2级大类、3级中类、4级小类）
- 支持层级筛选和代码搜索
- 父子关系维护

**数据表结构:**
```sql
industry_classification (
  id, code, level, level_name, primary_name,
  detailed_description, full_path_description, parent_code
)
```

### 2. 开发区管理模块 (`/admin/parks`)

**核心功能:**
- 展示所有开发区（包括三线一单中的和新增的）
- 支持新增开发区（会自动出现在前端选择列表中）
- 为每个开发区设置多个要求
- 区域关联关系管理

**数据表扩展:**
```sql
-- 开发区要求表（新增）
CREATE TABLE park_requirements (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  park_id INTEGER NOT NULL,
  requirement_title VARCHAR(200) NOT NULL,
  requirement_content TEXT NOT NULL,
  requirement_type VARCHAR(50), -- 准入要求/环保要求/安全要求等
  priority INTEGER DEFAULT 0,   -- 优先级
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (park_id) REFERENCES parks(id)
);

-- 开发区状态扩展
ALTER TABLE parks ADD COLUMN is_from_sanxian BOOLEAN DEFAULT FALSE; -- 是否来自三线一单
ALTER TABLE parks ADD COLUMN description TEXT;                      -- 开发区描述
ALTER TABLE parks ADD COLUMN status VARCHAR(20) DEFAULT 'active';   -- 状态
```

### 3. 关注点管理模块

#### 3.1 行业关键词管理 (`/admin/keywords`)
**功能特点:**
- 按行业分组展示关键词
- 支持为行业批量添加关键词
- 关键词的增删改操作
- 关键词使用统计

#### 3.2 通用标签管理 (`/admin/general-tags`)
**功能特点:**
- 分页展示所有通用标签
- 标签的增删改操作
- 标签使用频率统计
- 标签分类管理

## 页面布局设计

### 主布局结构
```vue
<template>
  <el-container class="admin-layout">
    <!-- 顶部导航 -->
    <el-header class="admin-header">
      <div class="header-left">
        <h2>蓝天Wiki 后台管理</h2>
      </div>
      <div class="header-right">
        <el-dropdown>
          <span class="user-info">管理员 <i class="el-icon-arrow-down"></i></span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="admin-sidebar">
        <el-menu :default-active="$route.path" router>
          <el-menu-item index="/admin/dashboard">
            <i class="el-icon-data-analysis"></i>
            <span>仪表板</span>
          </el-menu-item>
          
          <el-sub-menu index="data">
            <template #title>
              <i class="el-icon-document"></i>
              <span>数据管理</span>
            </template>
            <el-menu-item index="/admin/control-units">三线一单</el-menu-item>
            <el-menu-item index="/admin/industries">分类管理名录</el-menu-item>
            <el-menu-item index="/admin/classifications">国民经济行业</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/admin/parks">
            <i class="el-icon-office-building"></i>
            <span>开发区管理</span>
          </el-menu-item>
          
          <el-sub-menu index="focus">
            <template #title>
              <i class="el-icon-collection-tag"></i>
              <span>关注点管理</span>
            </template>
            <el-menu-item index="/admin/keywords">行业关键词</el-menu-item>
            <el-menu-item index="/admin/general-tags">通用标签</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="admin-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>
```

## 权限控制设计

### 角色定义
- **超级管理员**: 所有权限
- **数据管理员**: 数据的增删改查
- **只读用户**: 仅查看权限

### 权限控制实现
```javascript
// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('admin_token')
  if (!token && to.path !== '/admin/login') {
    next('/admin/login')
  } else {
    next()
  }
})

// API权限中间件
const checkPermission = (permission) => {
  return (req, res, next) => {
    const userPermissions = req.user.permissions
    if (userPermissions.includes(permission)) {
      next()
    } else {
      res.status(403).json({ message: '权限不足' })
    }
  }
}
```

## 分页设计规范

### 统一分页参数
```javascript
{
  page: 1,           // 当前页码
  pageSize: 20,      // 每页条数
  total: 0,          // 总条数
  search: '',        // 搜索关键词
  filters: {}        // 筛选条件
}
```

### 分页组件封装
```vue
<template>
  <div class="pagination-wrapper">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
```

这个设计方案充分考虑了您提到的所有需求：

1. **分类展示现有内容** - 三个不同的数据管理模块，每个都有专门的分页展示
2. **支持增删改** - 每个模块都有完整的CRUD操作
3. **开发区独立设置** - 专门的开发区管理模块，支持新增和要求设置
4. **关注点管理** - 独立的关注点管理模块，支持分页和编辑

接下来我将开始实现具体的代码。您希望我先从哪个模块开始？
