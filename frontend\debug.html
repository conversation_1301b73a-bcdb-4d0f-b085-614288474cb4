<!DOCTYPE html>
<html>
<head>
    <title>Debug API</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Debug API Response</h1>
    <button onclick="testAPI()">测试API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            try {
                const response = await axios.get('http://localhost:3001/api/search/required', {
                    params: {
                        region_id: 1,
                        in_park: true,
                        park_id: 2,
                        industry_id: 1
                    }
                });
                
                console.log('API Response:', response.data);
                
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `
                    <h2>API响应:</h2>
                    <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    
                    <h2>园区要求数据:</h2>
                    ${response.data.data.park_policies ? 
                        response.data.data.park_policies.map(policy => `
                            <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
                                <h3>${policy.requirement_title}</h3>
                                <p><strong>分类:</strong> ${policy.category || '未设置'}</p>
                                <p><strong>内容:</strong> ${policy.requirement_content}</p>
                            </div>
                        `).join('') 
                        : '没有园区要求数据'
                    }
                `;
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `<p style="color: red;">错误: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
