const { getDatabase } = require('../config/database');

class ControlUnit {
  // 分页获取管控单元
  static getPaginated(page = 1, pageSize = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const offset = (page - 1) * pageSize;
      
      let whereClause = 'WHERE 1=1';
      let params = [];
      
      // 搜索条件
      if (filters.search) {
        whereClause += ' AND (cu.unit_name LIKE ? OR cu.unit_code LIKE ?)';
        params.push(`%${filters.search}%`, `%${filters.search}%`);
      }
      
      if (filters.region_id) {
        whereClause += ' AND cu.region_id = ?';
        params.push(filters.region_id);
      }
      
      if (filters.unit_type) {
        whereClause += ' AND cu.unit_type LIKE ?';
        params.push(`%${filters.unit_type}%`);
      }
      
      // 获取总数
      const countSql = `
        SELECT COUNT(*) as total 
        FROM control_units cu
        LEFT JOIN regions r ON cu.region_id = r.id
        LEFT JOIN parks p ON cu.park_id = p.id
        ${whereClause}
      `;
      
      db.get(countSql, params, (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 获取分页数据
        const dataSql = `
          SELECT cu.*, r.name as region_name, p.name as park_name
          FROM control_units cu
          LEFT JOIN regions r ON cu.region_id = r.id
          LEFT JOIN parks p ON cu.park_id = p.id
          ${whereClause}
          ORDER BY cu.unit_name
          LIMIT ? OFFSET ?
        `;
        const dataParams = [...params, pageSize, offset];
        
        db.all(dataSql, dataParams, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total: countResult.total,
              page,
              pageSize,
              totalPages: Math.ceil(countResult.total / pageSize)
            });
          }
        });
      });
    });
  }

  // 根据ID获取管控单元
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT cu.*, r.name as region_name, p.name as park_name
        FROM control_units cu
        LEFT JOIN regions r ON cu.region_id = r.id
        LEFT JOIN parks p ON cu.park_id = p.id
        WHERE cu.id = ?
      `;
      db.get(sql, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建管控单元
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const {
        policy_id, unit_code, unit_name, region_id, park_id, unit_type,
        spatial_constraints, pollution_control, risk_prevention, resource_efficiency
      } = data;
      
      db.run(
        `INSERT INTO control_units (
          policy_id, unit_code, unit_name, region_id, park_id, unit_type,
          spatial_constraints, pollution_control, risk_prevention, resource_efficiency
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          policy_id, unit_code, unit_name, region_id, park_id, unit_type,
          spatial_constraints, pollution_control, risk_prevention, resource_efficiency
        ],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, ...data });
          }
        }
      );
    });
  }

  // 更新管控单元
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const {
        unit_code, unit_name, region_id, park_id, unit_type,
        spatial_constraints, pollution_control, risk_prevention, resource_efficiency
      } = data;
      
      db.run(
        `UPDATE control_units SET 
          unit_code = ?, unit_name = ?, region_id = ?, park_id = ?, unit_type = ?,
          spatial_constraints = ?, pollution_control = ?, risk_prevention = ?, resource_efficiency = ?
         WHERE id = ?`,
        [
          unit_code, unit_name, region_id, park_id, unit_type,
          spatial_constraints, pollution_control, risk_prevention, resource_efficiency, id
        ],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id, ...data });
          }
        }
      );
    });
  }

  // 删除管控单元
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM control_units WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }

  // 根据地区筛选管控单元（排除园区）
  static getByRegion(regionId) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT * FROM control_units 
        WHERE region_id = ? AND park_id IS NULL
        ORDER BY unit_name
      `;
      
      db.all(sql, [regionId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 搜索管控单元
  static search(location, unitType) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      let sql = `
        SELECT cu.*, r.name as county 
        FROM control_units cu
        LEFT JOIN regions r ON cu.region_id = r.id
        WHERE 1=1
      `;
      const params = [];
      
      // 根据位置模糊查询
      if (location) {
        sql += ` AND (cu.unit_name LIKE ? OR r.name LIKE ?)`;
        params.push(`%${location}%`, `%${location}%`);
      }
      
      // 根据管控单元类型筛选
      if (unitType) {
        sql += ` AND cu.unit_type = ?`;
        params.push(unitType);
      }
      
      sql += ` ORDER BY cu.unit_name`;
      
      db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}

module.exports = ControlUnit;
