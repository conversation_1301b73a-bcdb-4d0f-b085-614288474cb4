const { initDatabase, getDatabase } = require('./config/database');

async function checkAgricultureData() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 检查农业相关数据 ===');
    
    // 1. 查看industry_classification表中的农业相关数据
    console.log('\n--- industry_classification表中的农业相关数据 ---');
    const agricultureClassifications = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM industry_classification 
        WHERE primary_name LIKE '%农%' OR primary_name LIKE '%林%' OR primary_name LIKE '%畜%' OR primary_name LIKE '%渔%'
        ORDER BY code
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('找到', agricultureClassifications.length, '条农业相关分类:');
    agricultureClassifications.forEach(cls => {
      console.log(`代码: ${cls.code}, 名称: ${cls.primary_name}, 层级: ${cls.level}`);
    });
    
    // 2. 查看industries表中的农业相关数据
    console.log('\n--- industries表中的农业相关数据 ---');
    const agricultureIndustries = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM industries 
        WHERE name LIKE '%农%' OR name LIKE '%林%' OR name LIKE '%畜%' OR name LIKE '%渔%'
        ORDER BY id
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('找到', agricultureIndustries.length, '条农业相关行业:');
    agricultureIndustries.forEach(industry => {
      console.log(`ID: ${industry.id}, 名称: ${industry.name}, 代码: ${industry.code || '无'}`);
    });
    
    // 3. 测试查找"农产品基地项目"
    console.log('\n--- 测试查找"农产品基地项目" ---');
    const testName = "农产品基地项目";
    const pureName = testName.replace(/（[^）]+）\s*\d*$/, '').replace(/[\d\*]+$/, '').trim();
    console.log('原始名称:', testName);
    console.log('清理后名称:', pureName);
    
    const matchedClassifications = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM industry_classification
        WHERE primary_name LIKE ?
        ORDER BY level ASC, length(primary_name) ASC
      `, [`%${pureName}%`], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('匹配的分类数量:', matchedClassifications.length);
    matchedClassifications.forEach(cls => {
      console.log(`匹配: 代码: ${cls.code}, 名称: ${cls.primary_name}, 层级: ${cls.level}`);
    });
    
    // 4. 尝试更宽泛的搜索
    console.log('\n--- 尝试更宽泛的搜索 ---');
    const broadMatches = await new Promise((resolve, reject) => {
      db.all(`
        SELECT * FROM industry_classification
        WHERE primary_name LIKE '%农产品%' OR primary_name LIKE '%农业%' OR primary_name LIKE '%种植%'
        ORDER BY level ASC
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('宽泛搜索结果:', broadMatches.length);
    broadMatches.forEach(cls => {
      console.log(`宽泛匹配: 代码: ${cls.code}, 名称: ${cls.primary_name}, 层级: ${cls.level}`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('检查失败:', error);
    process.exit(1);
  }
}

checkAgricultureData();
