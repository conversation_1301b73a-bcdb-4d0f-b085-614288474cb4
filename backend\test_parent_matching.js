const { initDatabase, getDatabase } = require('./config/database');
const IndustryClassification = require('./models/IndustryClassification');
const IndustryFocusPoint = require('./models/IndustryFocusPoint');

async function testParentMatching() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 测试父级匹配功能 ===');
    
    // 1. 先创建一些测试数据
    console.log('\n--- 创建测试数据 ---');
    
    // 获取农业相关的分类
    const agricultureClassifications = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, code, primary_name, level, parent_code
        FROM industry_classification 
        WHERE code LIKE 'A%' 
        ORDER BY level, code
        LIMIT 10
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('找到农业分类:', agricultureClassifications.length, '个');
    agricultureClassifications.forEach(cls => {
      console.log(`  ${cls.code} - ${cls.primary_name} (层级${cls.level}, 父级: ${cls.parent_code || '无'})`);
    });
    
    if (agricultureClassifications.length < 2) {
      console.log('农业分类数据不足，无法测试');
      process.exit(1);
    }
    
    // 找到一个大类和一个小类
    const parentClass = agricultureClassifications.find(c => c.level === 1); // 门类
    const childClass = agricultureClassifications.find(c => c.level > 1 && c.parent_code); // 子类
    
    if (!parentClass || !childClass) {
      console.log('没有找到合适的父子分类');
      process.exit(1);
    }
    
    console.log('\n选择测试分类:');
    console.log('父分类:', parentClass.primary_name, '(ID:', parentClass.id, ')');
    console.log('子分类:', childClass.primary_name, '(ID:', childClass.id, ')');
    
    // 2. 创建一个关注点，关联到父分类
    console.log('\n--- 创建关注点关联到父分类 ---');
    
    // 先检查是否已有关注点
    const existingPoint = await new Promise((resolve, reject) => {
      db.get('SELECT id, name FROM industry_focus_points WHERE is_active = 1 LIMIT 1', (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    if (!existingPoint) {
      console.log('没有找到现有关注点，创建一个测试关注点');
      
      const pointId = await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO industry_focus_points (name, description, is_active) VALUES (?, ?, ?)',
          ['测试农业关注点', '用于测试层级匹配的关注点', 1],
          function(err) {
            if (err) reject(err);
            else resolve(this.lastID);
          }
        );
      });
      
      console.log('创建了关注点，ID:', pointId);
      
      // 关联到父分类
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT OR REPLACE INTO industry_focus_point_relations (industry_focus_point_id, industry_classification_id) VALUES (?, ?)',
          [pointId, parentClass.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log('关注点已关联到父分类:', parentClass.primary_name);
    } else {
      console.log('使用现有关注点:', existingPoint.name, '(ID:', existingPoint.id, ')');
      
      // 确保关联到父分类
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT OR REPLACE INTO industry_focus_point_relations (industry_focus_point_id, industry_classification_id) VALUES (?, ?)',
          [existingPoint.id, parentClass.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log('关注点已关联到父分类:', parentClass.primary_name);
    }
    
    // 3. 测试父级匹配：选择子分类，应该能匹配到关联在父分类上的关注点
    console.log('\n--- 测试父级匹配 ---');
    
    // 获取子分类的所有父级ID
    const parentIds = await IndustryFocusPoint.getAllParentClassificationIds(childClass.id);
    console.log('子分类的所有父级ID:', parentIds);
    
    // 获取子分类信息
    const childClassificationInfo = await IndustryClassification.getClassificationInfo(childClass.id);
    console.log('子分类信息:', childClassificationInfo ? childClassificationInfo.primary_name : '未找到');
    
    // 获取匹配的关注点
    const matchedPoints = await IndustryFocusPoint.getByClassificationInfo(childClassificationInfo);
    console.log('匹配到的关注点数量:', matchedPoints.length);
    
    matchedPoints.forEach(point => {
      console.log(`✅ 匹配到: ${point.name} (关联分类: ${point.industry_names})`);
    });
    
    // 4. 验证结果
    if (matchedPoints.length > 0) {
      console.log('\n🎉 层级匹配测试成功！');
      console.log('✅ 用户选择子分类时，能够匹配到设置在父分类上的关注点');
    } else {
      console.log('\n❌ 层级匹配测试失败！');
      console.log('用户选择子分类时，无法匹配到设置在父分类上的关注点');
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testParentMatching();
