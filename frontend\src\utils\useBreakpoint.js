import { ref, onMounted, onUnmounted } from 'vue';

/**
 * A simple composable to detect screen size for responsive design.
 * @returns {{isMobile: import('vue').Ref<boolean>}}
 */
export function useBreakpoint() {
  const isMobile = ref(false);

  const checkScreen = () => {
    isMobile.value = window.innerWidth < 768;
  };

  onMounted(() => {
    checkScreen();
    window.addEventListener('resize', checkScreen);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', checkScreen);
  });

  return { isMobile };
} 