import { createRouter, createWebHistory } from 'vue-router'
import SearchPage from './views/SearchPage.vue'
import AdminLayout from './views/admin/AdminLayout.vue'
import Dashboard from './views/admin/Dashboard.vue'

const routes = [
  {
    path: '/',
    name: 'Search',
    component: SearchPage
  },
  {
    path: '/admin',
    component: AdminLayout,
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: Dashboard
      },
      {
        path: 'control-units',
        name: 'ControlUnits',
        component: () => import('./views/admin/ControlUnits.vue')
      },
      {
        path: 'industries',
        name: 'Industries',
        component: () => import('./views/admin/Industries.vue')
      },
      {
        path: 'classifications',
        name: 'Classifications',
        component: () => import('./views/admin/Classifications.vue')
      },
      {
        path: 'parks',
        name: 'Parks',
        component: () => import('./views/admin/Parks.vue')
      },
      {
        path: 'focus-points',
        name: 'FocusPoints',
        component: () => import('./views/admin/FocusPoints.vue')
      },
      {
        path: 'general-focus-points',
        name: 'GeneralFocusPoints',
        component: () => import('./views/admin/GeneralFocusPoints.vue')
      },
      {
        path: 'industry-focus-points',
        name: 'IndustryFocusPoints',
        component: () => import('./views/admin/IndustryFocusPoints.vue')
      },
      {
        path: 'park-requirement-categories',
        name: 'ParkRequirementCategories',
        component: () => import('./views/admin/ParkRequirementCategories.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 