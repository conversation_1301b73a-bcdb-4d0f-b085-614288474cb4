const { initDatabase, getDatabase } = require('./config/database');

async function checkFocusPointRelations() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 检查关注点关联数据 ===');
    
    // 1. 查看所有关注点
    console.log('\n--- 所有行业关注点 ---');
    const focusPoints = await new Promise((resolve, reject) => {
      db.all('SELECT id, name, is_active FROM industry_focus_points ORDER BY id', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('关注点数量:', focusPoints.length);
    focusPoints.forEach(point => {
      console.log(`  ID: ${point.id}, 名称: ${point.name}, 状态: ${point.is_active ? '启用' : '禁用'}`);
    });
    
    // 2. 查看所有关联关系
    console.log('\n--- 所有关联关系 ---');
    const relations = await new Promise((resolve, reject) => {
      db.all(`
        SELECT r.*, ifp.name as point_name, ic.primary_name as classification_name, ic.code, ic.level
        FROM industry_focus_point_relations r
        JOIN industry_focus_points ifp ON r.industry_focus_point_id = ifp.id
        JOIN industry_classification ic ON r.industry_classification_id = ic.id
        ORDER BY ifp.name, ic.code
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('关联关系数量:', relations.length);
    relations.forEach(rel => {
      console.log(`  ${rel.point_name} -> ${rel.classification_name} (${rel.code}, 层级${rel.level})`);
    });
    
    // 3. 查找制造业相关的分类
    console.log('\n--- 制造业相关分类 ---');
    const manufacturingClassifications = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, code, primary_name, level, parent_code
        FROM industry_classification 
        WHERE primary_name LIKE '%制造%' OR code LIKE 'C%'
        ORDER BY level, code
        LIMIT 10
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('制造业分类数量:', manufacturingClassifications.length);
    manufacturingClassifications.forEach(cls => {
      console.log(`  ID: ${cls.id}, 代码: ${cls.code}, 名称: ${cls.primary_name}, 层级: ${cls.level}`);
    });
    
    // 4. 检查是否有关注点关联到制造业
    if (manufacturingClassifications.length > 0) {
      const manufacturingIds = manufacturingClassifications.map(c => c.id);
      const placeholders = manufacturingIds.map(() => '?').join(',');
      
      const manufacturingRelations = await new Promise((resolve, reject) => {
        db.all(`
          SELECT r.*, ifp.name as point_name, ic.primary_name as classification_name
          FROM industry_focus_point_relations r
          JOIN industry_focus_points ifp ON r.industry_focus_point_id = ifp.id
          JOIN industry_classification ic ON r.industry_classification_id = ic.id
          WHERE r.industry_classification_id IN (${placeholders})
        `, manufacturingIds, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });
      
      console.log('\n--- 制造业关联的关注点 ---');
      console.log('数量:', manufacturingRelations.length);
      manufacturingRelations.forEach(rel => {
        console.log(`  ${rel.point_name} -> ${rel.classification_name}`);
      });
      
      if (manufacturingRelations.length === 0) {
        console.log('⚠️  没有关注点关联到制造业分类！');
        
        // 创建一个测试关联
        if (focusPoints.length > 0 && manufacturingClassifications.length > 0) {
          console.log('\n--- 创建测试关联 ---');
          const testPoint = focusPoints[0];
          const testClassification = manufacturingClassifications[0];
          
          await new Promise((resolve, reject) => {
            db.run(
              'INSERT OR REPLACE INTO industry_focus_point_relations (industry_focus_point_id, industry_classification_id) VALUES (?, ?)',
              [testPoint.id, testClassification.id],
              (err) => {
                if (err) reject(err);
                else resolve();
              }
            );
          });
          
          console.log(`✅ 已创建测试关联: ${testPoint.name} -> ${testClassification.primary_name}`);
        }
      }
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('检查失败:', error);
    process.exit(1);
  }
}

checkFocusPointRelations();
