<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
:root {
  --el-color-primary: #4a90e2;
  --el-color-primary-light-3: #79b2f2;
  --el-color-primary-light-5: #a0c9f5;
  --el-color-primary-light-7: #c8dff9;
  --el-color-primary-light-8: #ddebfb;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #3a73b4;
  
  --bg-color: #f7f9fc;
  --bg-color-gradient: linear-gradient(180deg, #ffffff 0%, #f7f9fc 100%);
  --card-bg-color: rgba(255, 255, 255, 0.9);
  --border-color: #e5e9f2;
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color-primary);
}

body {
  margin: 0;
  padding: 0;
  background: var(--bg-color-gradient);
  min-height: 100vh;
}

* {
  box-sizing: border-box;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
::-webkit-scrollbar-track {
    background: transparent;
}
::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}
::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

/* Transitions */
*, *::before, *::after {
    transition: background-color .3s ease, border-color .3s ease, color .3s ease, box-shadow .3s ease;
}
</style> 