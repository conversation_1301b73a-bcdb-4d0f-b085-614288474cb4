import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 创建管理后台axios实例
const adminApi = axios.create({
  baseURL: '/api/admin',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('API请求:', config.method.toUpperCase(), config.url, config.params || config.data)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

adminApi.interceptors.request.use(
  config => {
    console.log('Admin API请求:', config.method.toUpperCase(), config.url, config.params || config.data)
    return config
  },
  error => {
    console.error('Admin请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API响应:', response.config.url, response.data)
    return response.data
  },
  error => {
    console.error('响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

adminApi.interceptors.response.use(
  response => {
    console.log('Admin API响应:', response.config.url, response.data)
    return response.data
  },
  error => {
    console.error('Admin响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// API接口
export const apiService = {
  // 获取行业列表
  getIndustries() {
    return api.get('/industries')
  },

  // 获取行业环评要求
  getIndustryRequirements(industryId) {
    return api.get(`/industries/${industryId}/requirements`)
  },

  // 获取区域列表
  getRegions() {
    return api.get('/regions')
  },

  // 获取园区列表
  getParks(regionId) {
    return api.get('/parks', { params: { region_id: regionId } })
  },

  // 获取单个园区信息
  getParkById(parkId) {
    return api.get(`/parks/${parkId}`)
  },

  // 获取管控单元列表
  getControlUnits(regionId) {
    return api.get('/control-units', { params: { region_id: regionId } })
  },

  // 获取行业关键词
  getIndustryKeywords(industryId) {
    return api.get('/industry-keywords', { params: { industry_id: industryId } })
  },

  // 获取通用标签
  getGeneralTags() {
    return api.get('/general-tags')
  },

  // 获取关注点
  getFocusPoints(params) {
    return adminApi.get('/focus-points', { params })
  },

  // 获取通用关注点（用于搜索页面）
  getGeneralFocusPoints(params) {
    return adminApi.get('/general-focus-points', { params })
  },

  // 获取行业关注点（用于搜索页面）
  getIndustryFocusPointsByIndustry(industryId) {
    return api.get('/industry-focus-points/by-industry', { params: { industry_id: industryId } })
  },

  // 必选项查询
  searchRequired(params) {
    return api.get('/search/required', { params })
  },

  // 可选项查询
  searchOptional(params) {
    return api.get('/search/optional', { params })
  },

  // 三线一单查询
  searchSanxianyidan(data) {
    return api.post('/sanxianyidan', data)
  }
}

// 管理后台API接口
export const adminApiService = {
  // 三线一单管理
  getControlUnits(params) {
    return adminApi.get('/control-units', { params })
  },
  getControlUnit(id) {
    return adminApi.get(`/control-units/${id}`)
  },
  createControlUnit(data) {
    return adminApi.post('/control-units', data)
  },
  updateControlUnit(id, data) {
    return adminApi.put(`/control-units/${id}`, data)
  },
  deleteControlUnit(id) {
    return adminApi.delete(`/control-units/${id}`)
  },

  // 分类管理名录
  getIndustries(params) {
    return adminApi.get('/industries', { params })
  },
  getIndustryCategories() {
    return adminApi.get('/industries/categories')
  },
  getIndustry(id) {
    return adminApi.get(`/industries/${id}`)
  },
  createIndustry(data) {
    return adminApi.post('/industries', data)
  },
  updateIndustry(id, data) {
    return adminApi.put(`/industries/${id}`, data)
  },
  deleteIndustry(id) {
    return adminApi.delete(`/industries/${id}`)
  },

  // 国民经济行业分类
  getClassifications(params) {
    return adminApi.get('/classifications', { params })
  },
  getClassificationTree(params) {
    return adminApi.get('/classifications/tree', { params })
  },
  getClassification(id) {
    return adminApi.get(`/classifications/${id}`)
  },
  createClassification(data) {
    return adminApi.post('/classifications', data)
  },
  updateClassification(id, data) {
    return adminApi.put(`/classifications/${id}`, data)
  },
  deleteClassification(id) {
    return adminApi.delete(`/classifications/${id}`)
  },

  // 开发区管理
  getParks(params) {
    return adminApi.get('/parks', { params })
  },
  getPark(id) {
    return adminApi.get(`/parks/${id}`)
  },
  createPark(data) {
    return adminApi.post('/parks', data)
  },
  updatePark(id, data) {
    return adminApi.put(`/parks/${id}`, data)
  },
  deletePark(id) {
    return adminApi.delete(`/parks/${id}`)
  },
  getParkRequirements(id) {
    return adminApi.get(`/parks/${id}/requirements`)
  },
  addParkRequirement(id, data) {
    return adminApi.post(`/parks/${id}/requirements`, data)
  },
  updateParkRequirement(requirementId, data) {
    return adminApi.put(`/park-requirements/${requirementId}`, data)
  },
  deleteParkRequirement(requirementId) {
    return adminApi.delete(`/park-requirements/${requirementId}`)
  },

  // 关注点管理
  getFocusPoints(params) {
    return adminApi.get('/focus-points', { params })
  },
  getFocusPointCategories() {
    return adminApi.get('/focus-points/categories')
  },
  getFocusPoint(id) {
    return adminApi.get(`/focus-points/${id}`)
  },
  createFocusPoint(data) {
    return adminApi.post('/focus-points', data)
  },
  updateFocusPoint(id, data) {
    return adminApi.put(`/focus-points/${id}`, data)
  },
  deleteFocusPoint(id) {
    return adminApi.delete(`/focus-points/${id}`)
  },
  updateFocusPointSortOrder(data) {
    return adminApi.put('/focus-points/sort-order', data)
  },

  // 通用关注点管理
  getGeneralFocusPoints(params) {
    return adminApi.get('/general-focus-points', { params })
  },
  getGeneralFocusPoint(id) {
    return adminApi.get(`/general-focus-points/${id}`)
  },
  createGeneralFocusPoint(data) {
    return adminApi.post('/general-focus-points', data)
  },
  updateGeneralFocusPoint(id, data) {
    return adminApi.put(`/general-focus-points/${id}`, data)
  },
  deleteGeneralFocusPoint(id) {
    return adminApi.delete(`/general-focus-points/${id}`)
  },

  // 行业关注点管理
  getIndustryFocusPoints(params) {
    return adminApi.get('/industry-focus-points', { params })
  },
  getIndustryFocusPoint(id) {
    return adminApi.get(`/industry-focus-points/${id}`)
  },
  createIndustryFocusPoint(data) {
    return adminApi.post('/industry-focus-points', data)
  },
  updateIndustryFocusPoint(id, data) {
    return adminApi.put(`/industry-focus-points/${id}`, data)
  },
  deleteIndustryFocusPoint(id) {
    return adminApi.delete(`/industry-focus-points/${id}`)
  },

  // 辅助接口
  getRegions() {
    return adminApi.get('/regions')
  },

  // ==================== 园区要求分类管理 ====================

  // 获取所有园区要求分类
  getParkRequirementCategories() {
    return adminApi.get('/park-requirement-categories')
  },

  // 创建园区要求分类
  createParkRequirementCategory(data) {
    return adminApi.post('/park-requirement-categories', data)
  },

  // 更新园区要求分类
  updateParkRequirementCategory(id, data) {
    return adminApi.put(`/park-requirement-categories/${id}`, data)
  },

  // 删除园区要求分类
  deleteParkRequirementCategory(id) {
    return adminApi.delete(`/park-requirement-categories/${id}`)
  }
}

export default api
export { adminApi }