<template>
  <div v-if="industryRequirements" class="result-block">
    <h3 class="block-title">
      <i class="icon-check"></i>
      {{ industryRequirements.tableHeader.title }}
    </h3>
    
    <!-- 大类标题 -->
    <div class="category-banner">
      <h4>{{ industryRequirements.industry.category }}</h4>
    </div>

    <!-- 环评要求表格 -->
    <div class="table-wrapper">
      <el-table 
        :data="industryRequirements.requirements" 
        border 
        stripe
        class="requirements-table"
        :header-cell-style="{ 
          background: '#f8fafc', 
          color: '#374151', 
          fontWeight: '600',
          textAlign: 'center',
          borderColor: '#e5e7eb'
        }"
        :cell-style="{ 
          padding: '16px 12px',
          borderColor: '#e5e7eb'
        }"
      >
        <el-table-column 
          prop="projectTypes" 
          :label="industryRequirements.tableHeader.columns[0]" 
          width="220"
          :show-overflow-tooltip="{ effect: 'dark', placement: 'top' }"
        >
          <template #default="scope">
            <div class="cell-text">{{ scope.row.projectTypes }}</div>
          </template>
        </el-table-column>
        <el-table-column 
          prop="reportBook" 
          :label="industryRequirements.tableHeader.columns[1]" 
          width="200"
          :show-overflow-tooltip="{ effect: 'dark', placement: 'top' }"
        >
          <template #default="scope">
            <div class="cell-text">{{ scope.row.reportBook }}</div>
          </template>
        </el-table-column>
        <el-table-column 
          prop="reportTable" 
          :label="industryRequirements.tableHeader.columns[2]" 
          width="200"
          :show-overflow-tooltip="{ effect: 'dark', placement: 'top' }"
        >
          <template #default="scope">
            <div class="cell-text">{{ scope.row.reportTable }}</div>
          </template>
        </el-table-column>
        <el-table-column 
          prop="registrationForm" 
          :label="industryRequirements.tableHeader.columns[3]" 
          width="180"
          :show-overflow-tooltip="{ effect: 'dark', placement: 'top' }"
        >
          <template #default="scope">
            <div class="cell-text">{{ scope.row.registrationForm }}</div>
          </template>
        </el-table-column>
        <el-table-column 
          prop="sensitiveAreaMeaning" 
          :label="industryRequirements.tableHeader.columns[4]"
          min-width="300"
          :show-overflow-tooltip="{ effect: 'dark', placement: 'top' }"
        >
          <template #default="scope">
            <div class="cell-text">{{ scope.row.sensitiveAreaMeaning }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 说明 -->
    <div class="notice-box">
      <h5 class="notice-title">说明</h5>
      <div class="notice-content">
        <p><strong>1.</strong> 名录中项目类别后的数字为《国民经济行业分类》（GB/T4754-2017）及第1号修改单行业代码。</p>
        <p><strong>2.</strong> 名录中涉及规模的，均指新增规模。</p>
        <p><strong>3.</strong> 单纯混合指不发生化学反应的物理混合过程；分装指由大包装变为小包装。</p>
        <p><strong>4.</strong> 名录中所标"*"号，指在工业建筑中生产的建设项目。工业建筑的定义参见《工程结构设计基本术语标准》（GB/T50083-2014），指提供生产用的各种建筑物，如车间、厂前区建筑、生活间、动力站、库房和运输设施等。</p>
        <p><strong>5.</strong> 参照《中华人民共和国环境保护税法实施条例》，建设城乡污水集中处理工程，是指为社会公众提供生活污水处理服务的工程，不包括为工业园区、开发区等工业聚集区域内的企业事业单位和其他生产经营者提供污水处理服务的工程，以及建设单位自建自用的污水处理工程。</p>
        <p><strong>6.</strong> 化学镀、阳极氧化生产工艺按照本名录中电镀工艺相关规定执行。</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IndustryRequirementsTable',
  props: {
    industryRequirements: {
      type: Object,
      default: null
    }
  }
}
</script>

<style scoped>
/* 图标字体 */
.icon-check::before { content: '✅'; }

.result-block {
  padding: 30px;
  border-bottom: 1px solid #f1f5f9;
}

.result-block:last-child {
  border-bottom: none;
}

.block-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 大类标题 */
.category-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  margin: 0 0 20px 0;
  border-radius: 8px;
  text-align: center;
}

.category-banner h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 表格样式 */
.table-wrapper {
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.requirements-table {
  width: 100%;
}

.cell-text {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
}

/* 说明框 */
.notice-box {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.notice-title {
  color: #0369a1;
  font-weight: 600;
  margin: 0 0 15px 0;
  font-size: 1rem;
}

.notice-content {
  color: #0c4a6e;
  line-height: 1.6;
}

.notice-content p {
  margin: 0 0 10px 0;
}

.notice-content p:last-child {
  margin-bottom: 0;
}

/* Element Plus 表格样式覆盖 */
:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table th) {
  background: #f8fafc !important;
  color: #374151 !important;
  font-weight: 600 !important;
}

:deep(.el-table--border) {
  border: none;
}

:deep(.el-table--border::after) {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-wrapper {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .result-block {
    padding: 20px 15px;
  }
}
</style> 