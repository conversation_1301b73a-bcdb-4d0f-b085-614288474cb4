<template>
  <div class="desktop-industry-selector">
    <el-button
      @click="dialogVisible = true"
      :disabled="disabled"
      size="large"
      class="full-width selector-button"
    >
      <span class="button-content">
        <el-icon><Files /></el-icon>
        <span class="button-text">{{ selectedIndustryName || placeholder }}</span>
        <el-icon class="arrow-icon"><ArrowDown /></el-icon>
      </span>
    </el-button>

    <el-dialog
      v-model="dialogVisible"
      title="选择行业分类"
      width="1000px"
      top="5vh"
      :before-close="handleClose"
      class="industry-dialog"
      :z-index="9999"
      append-to-body
      destroy-on-close
    >
      <div class="industry-selector-content">
        <!-- 搜索框 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索行业名称，支持拼音首字母..."
            size="large"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 搜索结果 -->
        <div class="search-results" v-if="searchKeyword">
          <div class="results-header" v-if="searchResults.length > 0">
            <span class="results-count">找到 {{ searchResults.length }} 个匹配结果</span>
          </div>
          <div class="results-list" v-if="searchResults.length > 0">
            <div
              v-for="result in searchResults"
              :key="result.id"
              class="result-item"
              @click="selectIndustry(result)"
            >
              <div class="result-name">{{ result.name }}</div>
              <div class="result-path">{{ result.path }}</div>
            </div>
          </div>
          <div class="empty-state" v-else>
            <el-icon class="empty-icon"><Search /></el-icon>
            <div class="empty-text">未找到匹配的行业分类</div>
            <div class="empty-hint">请尝试其他关键词或拼音首字母</div>
          </div>
        </div>

        <!-- 左右分栏浏览 -->
        <div class="tree-browser" v-else>
          <!-- 左侧树形导航 -->
          <div class="tree-panel">
            <div class="panel-header">
              <span class="panel-title">行业分类</span>
              <el-button
                v-if="currentPath.length > 0"
                @click="goToRoot"
                text
                size="small"
              >
                返回根目录
              </el-button>
            </div>
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="treeData"
                :props="{ children: 'children', label: 'name' }"
                node-key="id"
                :expand-on-click-node="false"
                :highlight-current="true"
                @node-click="handleTreeNodeClick"
                class="industry-tree"
              >
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <span class="node-label">{{ data.name }}</span>
                    <span v-if="data.children && data.children.length > 0" class="node-count">
                      ({{ data.children.length }})
                    </span>
                  </div>
                </template>
              </el-tree>
            </div>
          </div>

          <!-- 右侧详情列表 -->
          <div class="detail-panel">
            <div class="panel-header">
              <span class="panel-title">
                {{ currentNode ? currentNode.name : '请选择左侧分类' }}
              </span>
              <div class="breadcrumb" v-if="currentPath.length > 0">
                <span
                  v-for="(item, index) in currentPath"
                  :key="item.id"
                  class="breadcrumb-item"
                >
                  {{ item.name }}
                  <el-icon v-if="index < currentPath.length - 1"><ArrowRight /></el-icon>
                </span>
              </div>
            </div>
            <div class="detail-content">
              <div v-if="currentChildren.length > 0" class="children-list">
                <div
                  v-for="child in currentChildren"
                  :key="child.id"
                  class="child-item"
                  @click="handleChildClick(child)"
                >
                  <div class="child-content">
                    <div class="child-name">{{ child.name }}</div>
                    <div class="child-info">
                      <span v-if="child.children && child.children.length > 0" class="has-children">
                        包含 {{ child.children.length }} 个子分类
                      </span>
                      <span v-else class="is-leaf">可直接选择</span>
                    </div>
                  </div>
                  <div class="child-action">
                    <el-button
                      v-if="!child.children || child.children.length === 0"
                      type="primary"
                      size="small"
                      @click.stop="selectIndustry(child)"
                    >
                      选择
                    </el-button>
                    <el-icon v-else class="expand-icon"><ArrowRight /></el-icon>
                  </div>
                </div>
              </div>
              <div v-else-if="currentNode && (!currentNode.children || currentNode.children.length === 0)" class="leaf-node">
                <div class="leaf-content">
                  <el-icon class="leaf-icon"><Check /></el-icon>
                  <div class="leaf-text">
                    <div class="leaf-name">{{ currentNode.name }}</div>
                    <div class="leaf-desc">这是一个具体的行业分类，可以直接选择</div>
                  </div>
                  <el-button type="primary" @click="selectIndustry(currentNode)">
                    选择此行业
                  </el-button>
                </div>
              </div>
              <div v-else class="empty-detail">
                <el-icon class="empty-icon"><FolderOpened /></el-icon>
                <div class="empty-text">请在左侧选择一个分类</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="selected-info" v-if="tempSelectedIndustry">
            <span class="selected-label">已选择：</span>
            <span class="selected-name">{{ tempSelectedIndustry.name }}</span>
          </div>
          <div class="footer-actions">
            <el-button @click="handleClose" size="large">取消</el-button>
            <el-button
              type="primary"
              @click="confirmSelection"
              :disabled="!tempSelectedIndustry"
              size="large"
            >
              确定选择
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ArrowDown, ArrowRight, Search, Check, Files, FolderOpened } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择行业'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const dialogVisible = ref(false)
const searchKeyword = ref('')
const currentPath = ref([])
const currentNode = ref(null)
const currentChildren = ref([])
const tempSelectedIndustry = ref(null)
const searchResults = ref([])
const treeRef = ref(null)

// 树形数据
const treeData = computed(() => props.options)

// 初始化
watch(() => props.options, (newOptions) => {
  if (newOptions && newOptions.length > 0) {
    currentChildren.value = newOptions
  }
}, { immediate: true })

// 计算选中的行业名称
const selectedIndustryName = computed(() => {
  if (!props.modelValue || props.modelValue.length === 0) return ''

  const findIndustryName = (options, targetId) => {
    for (const option of options) {
      if (option.id === targetId) {
        return option.name
      }
      if (option.children) {
        const found = findIndustryName(option.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  const lastId = props.modelValue[props.modelValue.length - 1]
  return findIndustryName(props.options, lastId) || ''
})

// 处理树节点点击
const handleTreeNodeClick = (data, node) => {
  currentNode.value = data
  currentChildren.value = data.children || []

  // 构建路径
  const path = []
  let current = node
  while (current && current.parent) {
    path.unshift(current.data)
    current = current.parent
  }
  currentPath.value = path

  // 高亮当前节点
  nextTick(() => {
    if (treeRef.value) {
      treeRef.value.setCurrentKey(data.id)
    }
  })
}

// 处理子项点击
const handleChildClick = (child) => {
  if (child.children && child.children.length > 0) {
    // 有子分类，在树中展开并选中
    nextTick(() => {
      if (treeRef.value) {
        treeRef.value.setCurrentKey(child.id)
        handleTreeNodeClick(child, { data: child, parent: { data: currentNode.value } })
      }
    })
  }
}

// 选择行业
const selectIndustry = (industry) => {
  tempSelectedIndustry.value = industry

  // 构建完整路径 - 找到从根到选中项的完整路径
  const findFullPath = (options, targetId, path = []) => {
    for (const option of options) {
      const currentPath = [...path, option]
      if (option.id === targetId) {
        return currentPath
      }
      if (option.children) {
        const found = findFullPath(option.children, targetId, currentPath)
        if (found) return found
      }
    }
    return null
  }

  const fullPath = findFullPath(props.options, industry.id)
  if (fullPath) {
    const industryIds = fullPath.map(item => item.id)
    emit('update:modelValue', industryIds)
    emit('change', industryIds)
  }
}

// 返回根目录
const goToRoot = () => {
  currentNode.value = null
  currentChildren.value = props.options
  currentPath.value = []
  if (treeRef.value) {
    treeRef.value.setCurrentKey(null)
  }
}

// 搜索处理
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = []
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const results = []

  const searchInOptions = (options, path = []) => {
    for (const option of options) {
      // 支持名称匹配和拼音首字母匹配
      const name = option.name.toLowerCase()
      const pinyin = getPinyin(option.name).toLowerCase()

      if (name.includes(keyword) || pinyin.includes(keyword)) {
        results.push({
          ...option,
          path: path.length > 0 ? path.map(p => p.name).join(' > ') : '顶级分类'
        })
      }

      if (option.children) {
        searchInOptions(option.children, [...path, option])
      }
    }
  }

  searchInOptions(props.options)
  searchResults.value = results.slice(0, 50) // 限制搜索结果数量
}

// 简单的拼音首字母提取（实际项目中可以使用专门的拼音库）
const getPinyin = (text) => {
  // 这里简化处理，实际可以集成 pinyin 库
  return text
}

// 确认选择
const confirmSelection = () => {
  if (tempSelectedIndustry.value) {
    dialogVisible.value = false
    resetDialog()
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetDialog()
}

// 重置对话框状态
const resetDialog = () => {
  searchKeyword.value = ''
  searchResults.value = []
  currentPath.value = []
  currentNode.value = null
  currentChildren.value = props.options
  tempSelectedIndustry.value = null
  if (treeRef.value) {
    treeRef.value.setCurrentKey(null)
  }
}
</script>

<style scoped>
.desktop-industry-selector {
  width: 100%;
}

.selector-button {
  width: 100%;
  height: 40px;
  border: 1px solid #dcdfe6;
  background: white;
  color: #606266;
  text-align: left;
  padding: 0 12px;
}

.selector-button:hover {
  border-color: #c0c4cc;
}

.selector-button:focus {
  border-color: #409eff;
}

.button-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.button-text {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow-icon {
  color: #c0c4cc;
  transition: transform 0.3s;
}

.industry-selector-content {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.search-results {
  flex: 1;
  overflow-y: auto;
}

.results-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.results-count {
  font-size: 14px;
  color: #909399;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-item {
  padding: 12px 16px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.result-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
  transform: translateX(4px);
}

.result-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.result-path {
  font-size: 12px;
  color: #909399;
}

.tree-browser {
  display: flex;
  height: 550px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.tree-panel {
  width: 300px;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  background: #fafbfc;
}

.detail-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.panel-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tree-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.industry-tree {
  background: transparent;
}

:deep(.industry-tree .el-tree-node__content) {
  height: 32px;
  padding: 0 8px;
  border-radius: 4px;
  margin-bottom: 2px;
}

:deep(.industry-tree .el-tree-node__content:hover) {
  background: #e6f7ff;
}

:deep(.industry-tree .el-tree-node.is-current > .el-tree-node__content) {
  background: #409eff;
  color: white;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 4px;
}

.node-label {
  flex: 1;
  font-size: 13px;
}

.node-count {
  font-size: 11px;
  color: #909399;
}

:deep(.industry-tree .el-tree-node.is-current .node-count) {
  color: rgba(255, 255, 255, 0.8);
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.children-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.child-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.child-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.child-content {
  flex: 1;
}

.child-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.child-info {
  font-size: 12px;
  color: #909399;
}

.has-children {
  color: #409eff;
}

.is-leaf {
  color: #67c23a;
}

.child-action {
  margin-left: 12px;
}

.expand-icon {
  color: #c0c4cc;
  font-size: 16px;
}

.leaf-node {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.leaf-content {
  text-align: center;
  padding: 24px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  background: #fafbfc;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.leaf-icon {
  font-size: 32px;
  color: #67c23a;
}

.leaf-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.leaf-desc {
  font-size: 14px;
  color: #909399;
}

.empty-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #c0c4cc;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

.empty-hint {
  font-size: 14px;
  margin-top: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0 0;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selected-label {
  font-size: 14px;
  color: #909399;
}

.selected-name {
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

:deep(.industry-dialog) {
  margin-top: 5vh !important;
  z-index: 9999 !important;
}

:deep(.industry-dialog .el-overlay) {
  z-index: 9998 !important;
}

:deep(.industry-dialog .el-dialog) {
  z-index: 9999 !important;
}

:deep(.industry-dialog .el-dialog__body) {
  padding: 20px;
}

:deep(.industry-dialog .el-dialog__header) {
  padding: 20px 20px 0;
}

:deep(.industry-dialog .el-dialog__footer) {
  padding: 0 20px 20px;
}

/* 全局确保弹窗层级 */
:global(.el-overlay) {
  z-index: 9998 !important;
}

:global(.el-dialog) {
  z-index: 9999 !important;
}

/* 特别针对行业选择器弹窗 */
:global(.industry-dialog) {
  z-index: 9999 !important;
}

:global(.industry-dialog .el-overlay) {
  z-index: 9998 !important;
}

:global(.industry-dialog .el-dialog) {
  z-index: 9999 !important;
}
</style>
