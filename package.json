{"name": "xinxiang-environmental-system", "version": "1.0.0", "description": "新乡市环保合规查询系统", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["环保", "合规", "查询系统", "新乡市"], "author": "新乡市环保合规查询系统团队", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}