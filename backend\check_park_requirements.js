const { initDatabase, getDatabase } = require('./config/database');

async function checkParkRequirements() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 检查园区要求数据 ===');
    
    // 检查表结构
    console.log('\n--- 表结构 ---');
    const tableInfo = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(park_requirements)", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('字段列表:');
    tableInfo.forEach(col => {
      console.log(`  ${col.name} (${col.type}) - ${col.notnull ? '必填' : '可选'} ${col.dflt_value ? `默认: ${col.dflt_value}` : ''}`);
    });
    
    // 检查现有数据
    console.log('\n--- 现有数据 ---');
    const requirements = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM park_requirements ORDER BY id', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('园区要求数量:', requirements.length);
    requirements.forEach(req => {
      console.log(`ID: ${req.id}`);
      console.log(`  标题: ${req.requirement_title}`);
      console.log(`  内容: ${req.requirement_content.substring(0, 50)}...`);
      console.log(`  类型: ${req.requirement_type || '空'}`);
      console.log(`  分类: ${req.category || '空'}`);
      console.log(`  园区ID: ${req.park_id}`);
      console.log('---');
    });
    
    // 检查是否有category字段
    const hasCategory = tableInfo.some(col => col.name === 'category');
    console.log('\n--- 字段检查 ---');
    console.log('是否有category字段:', hasCategory);
    console.log('是否有requirement_type字段:', tableInfo.some(col => col.name === 'requirement_type'));
    
    if (!hasCategory) {
      console.log('\n⚠️  缺少category字段，需要添加！');
    }
    
    // 检查分类数据
    console.log('\n--- 分类数据 ---');
    const categories = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM park_requirement_categories ORDER BY sort_order', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('可用分类:');
    categories.forEach(cat => {
      console.log(`  ${cat.name} (排序: ${cat.sort_order})`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('检查失败:', error);
    process.exit(1);
  }
}

checkParkRequirements();
