const http = require('http');

async function testParkFilesApi() {
  try {
    console.log('=== 测试园区文件API ===');
    
    // 1. 测试获取园区列表
    console.log('\n1. 测试获取园区列表...');
    const parksResponse = await makeRequest('http://localhost:3001/api/parks?region_id=1');
    console.log('园区列表:', JSON.stringify(parksResponse.data, null, 2));
    
    if (parksResponse.data && parksResponse.data.length > 0) {
      const parkId = parksResponse.data[0].id;
      
      // 2. 测试获取单个园区信息
      console.log(`\n2. 测试获取园区 ${parkId} 的详细信息...`);
      const parkResponse = await makeRequest(`http://localhost:3001/api/parks/${parkId}`);
      console.log('园区详情:', JSON.stringify(parkResponse.data, null, 2));
      
      // 3. 检查文件数据
      if (parkResponse.data && parkResponse.data.files) {
        console.log('\n3. 园区文件数据:');
        try {
          const files = JSON.parse(parkResponse.data.files);
          console.log('文件列表:', JSON.stringify(files, null, 2));
          console.log(`共 ${files.length} 个文件`);
        } catch (error) {
          console.log('文件数据解析失败:', error.message);
        }
      } else {
        console.log('\n3. 该园区没有文件数据');
      }
      
      // 4. 测试搜索API（包含园区文件）
      console.log('\n4. 测试搜索API...');
      const searchUrl = `http://localhost:3001/api/search/required?region_id=1&in_park=true&park_id=${parkId}&industry_id=1`;
      const searchResponse = await makeRequest(searchUrl);
      console.log('搜索结果:', JSON.stringify(searchResponse.data, null, 2));
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (e) {
          reject(e);
        }
      });
    });
    req.on('error', reject);
  });
}

testParkFilesApi();
