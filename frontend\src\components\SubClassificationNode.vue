<template>
  <div class="sub-classification-item">
    <!-- Display current node data -->
    <div class="sub-classification-header">
      <span class="sub-code">{{ node.code }}</span>
      <span class="level-badge" :class="`level-${node.level}`">{{ node.level_name }}</span>
      <span class="sub-name">{{ node.primary_name }}</span>
    </div>
    <div class="sub-description" v-if="node.detailed_description">
      <div v-html="formatDescription(node.detailed_description)"></div>
    </div>

    <!-- Recursive call for children -->
    <div class="nested-list" v-if="node.children && node.children.length > 0">
      <SubClassificationNode 
        v-for="child in node.children"
        :key="child.code"
        :node="child"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubClassificationNode',
  props: {
    node: {
      type: Object,
      required: true
    }
  },
  methods: {
    formatDescription(description) {
      if (!description) return '';
      // Simple formatting, can be expanded
      return description
        .replace(/◇ 包括[^：]*：/g, '<div class="include-section"><strong>$&</strong></div>')
        .replace(/◆ 不包括：/g, '<div class="exclude-section"><strong>$&</strong></div>')
        .replace(/— ([^；\n]+)[；\n]/g, '<div class="list-item">• $1</div>')
        .replace(/\n/g, '<br/>');
    }
  }
}
</script>

<style scoped>
.sub-classification-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-top: 10px;
}

.sub-classification-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.sub-code {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 14px;
}

.sub-name {
  font-weight: 500;
}

.sub-description {
  font-size: 13px;
  color: #5a6c7d;
  line-height: 1.5;
  margin-top: 8px;
}

.nested-list {
  margin-left: 20px;
  border-left: 2px solid #f1f5f9;
  padding-left: 15px;
  margin-top: 10px;
}

.level-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}
.level-1 { background-color: #e74c3c; }
.level-2 { background-color: #f39c12; }
.level-3 { background-color: #3498db; }
.level-4 { background-color: #27ae60; }
</style> 