const { getDatabase } = require('../config/database');

class FocusPoint {
  // 分页获取关注点
  static getPaginated(page = 1, pageSize = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const offset = (page - 1) * pageSize;

      let whereClause = 'WHERE 1=1';
      let params = [];

      // 搜索条件
      if (filters.search) {
        whereClause += ' AND (fp.name LIKE ? OR fp.description LIKE ? OR fp.keywords LIKE ?)';
        params.push(`%${filters.search}%`, `%${filters.search}%`, `%${filters.search}%`);
      }

      if (filters.category) {
        whereClause += ' AND fp.category = ?';
        params.push(filters.category);
      }

      if (filters.point_type) {
        whereClause += ' AND fp.point_type = ?';
        params.push(filters.point_type);
      }

      if (filters.is_active !== undefined) {
        whereClause += ' AND fp.is_active = ?';
        params.push(filters.is_active);
      }
      
      // 获取总数
      const countSql = `
        SELECT COUNT(*) as total
        FROM focus_points fp
        LEFT JOIN industries i ON fp.industry_id = i.id
        ${whereClause}
      `;
      
      db.get(countSql, params, (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 获取分页数据
        const dataSql = `
          SELECT fp.*, i.name as industry_name
          FROM focus_points fp
          LEFT JOIN industries i ON fp.industry_id = i.id
          ${whereClause}
          ORDER BY fp.point_type, fp.sort_order, fp.id
          LIMIT ? OFFSET ?
        `;
        const dataParams = [...params, pageSize, offset];
        
        db.all(dataSql, dataParams, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total: countResult.total,
              page,
              pageSize,
              totalPages: Math.ceil(countResult.total / pageSize)
            });
          }
        });
      });
    });
  }

  // 获取所有关注点分类
  static getCategories() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT DISTINCT category 
        FROM focus_points 
        WHERE category IS NOT NULL AND is_active = 1
        ORDER BY category
      `;
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => row.category));
        }
      });
    });
  }

  // 根据ID获取关注点
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.get("SELECT * FROM focus_points WHERE id = ?", [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建关注点
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, category, keywords, point_type, industry_id, sort_order, is_active } = data;

      db.run(
        `INSERT INTO focus_points (name, description, category, keywords, point_type, industry_id, sort_order, is_active)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [name, description, category, keywords, point_type || 'general', industry_id, sort_order || 0, is_active !== false],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, ...data });
          }
        }
      );
    });
  }

  // 更新关注点
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, category, keywords, point_type, industry_id, sort_order, is_active } = data;

      db.run(
        `UPDATE focus_points SET
          name = ?, description = ?, category = ?, keywords = ?,
          point_type = ?, industry_id = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, description, category, keywords, point_type || 'general', industry_id, sort_order, is_active, id],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id, ...data });
          }
        }
      );
    });
  }

  // 删除关注点
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM focus_points WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }

  // 批量更新排序
  static updateSortOrder(updates) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");
        
        const stmt = db.prepare("UPDATE focus_points SET sort_order = ? WHERE id = ?");
        
        let completed = 0;
        let hasError = false;
        
        updates.forEach(update => {
          stmt.run([update.sort_order, update.id], (err) => {
            if (err && !hasError) {
              hasError = true;
              db.run("ROLLBACK");
              stmt.finalize();
              reject(err);
              return;
            }
            
            completed++;
            if (completed === updates.length && !hasError) {
              stmt.finalize();
              db.run("COMMIT", (err) => {
                if (err) {
                  reject(err);
                } else {
                  resolve({ updated: completed });
                }
              });
            }
          });
        });
      });
    });
  }

  // 获取活跃的关注点
  static getActive() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT * FROM focus_points 
        WHERE is_active = 1 
        ORDER BY sort_order, id
      `;
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据关键词搜索关注点
  static searchByKeywords(keywords) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      if (!keywords || keywords.length === 0) {
        resolve([]);
        return;
      }

      // 构建搜索条件
      const keywordConditions = keywords.map(() => 'keywords LIKE ?').join(' OR ');
      const sql = `
        SELECT * FROM focus_points
        WHERE is_active = 1 AND (${keywordConditions})
        ORDER BY sort_order, id
      `;

      const params = keywords.map(keyword => `%${keyword}%`);

      db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据ID数组获取关注点
  static getByIds(ids) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      if (!ids || ids.length === 0) {
        resolve([]);
        return;
      }

      // 构建IN查询条件
      const placeholders = ids.map(() => '?').join(',');
      const sql = `
        SELECT fp.*, i.name as industry_name
        FROM focus_points fp
        LEFT JOIN industries i ON fp.industry_id = i.id
        WHERE fp.is_active = 1 AND fp.id IN (${placeholders})
        ORDER BY fp.point_type, fp.sort_order, fp.id
      `;

      db.all(sql, ids, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}

module.exports = FocusPoint;
