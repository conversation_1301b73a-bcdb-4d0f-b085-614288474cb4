<template>
  <div class="industry-selector">
    <el-cascader
      v-model="selectedIndustries"
      :options="industryOptions"
      :props="cascaderProps"
      placeholder="请选择关联行业"
      clearable
      filterable
      multiple
      collapse-tags
      collapse-tags-tooltip
      :max-collapse-tags="3"
      style="width: 100%"
      popper-class="industry-selector-popper"
      :teleported="true"
      :popper-options="{ strategy: 'fixed' }"
      @change="handleChange"
    />
    
    <!-- 已选择的行业显示 -->
    <div v-if="selectedIndustryNames.length > 0" class="selected-industries">
      <div class="selected-title">已选择的行业：</div>
      <div class="selected-tags">
        <el-tag
          v-for="(name, index) in selectedIndustryNames"
          :key="index"
          closable
          @close="removeIndustry(index)"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ name }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { adminApiService } from '@/utils/api'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  // 数据源类型：'industries' 分类管理名录，'classifications' 国民经济行业分类
  dataSource: {
    type: String,
    default: 'industries'
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const industryOptions = ref([])
const selectedIndustries = ref([])

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  multiple: true,
  emitPath: false, // 只返回最后一级的值
  checkStrictly: true, // 可以选择任意一级
  expandTrigger: 'hover' // 鼠标悬停展开
}

// 计算已选择的行业名称
const selectedIndustryNames = computed(() => {
  const names = []
  
  const findIndustryName = (options, targetId) => {
    for (const option of options) {
      if (option.id === targetId) {
        return option.name
      }
      if (option.children && option.children.length > 0) {
        const found = findIndustryName(option.children, targetId)
        if (found) return found
      }
    }
    return null
  }
  
  selectedIndustries.value.forEach(id => {
    const name = findIndustryName(industryOptions.value, id)
    if (name) {
      names.push(name)
    }
  })
  
  return names
})

// 将国民经济行业分类数据转换为树形结构
const convertToTreeData = (data) => {
  if (!Array.isArray(data)) return []

  // 按层级分组
  const levelGroups = {}
  data.forEach(item => {
    if (!levelGroups[item.level]) {
      levelGroups[item.level] = []
    }
    levelGroups[item.level].push({
      id: item.id,
      name: item.primary_name,
      code: item.code,
      level: item.level,
      parent_code: item.parent_code,
      children: []
    })
  })

  // 构建树形结构
  const buildTree = (level) => {
    if (!levelGroups[level]) return []

    return levelGroups[level].map(item => {
      const children = buildTree(level + 1).filter(child =>
        child.parent_code === item.code
      )
      return {
        ...item,
        children: children.length > 0 ? children : undefined
      }
    })
  }

  return buildTree(1) // 从第1级开始构建
}

// 加载行业数据
const loadIndustries = async () => {
  try {
    let response, result

    if (props.dataSource === 'classifications') {
      // 使用国民经济行业分类
      response = await adminApiService.getClassifications({ pageSize: 1000 })
      result = response.data

      // 转换为级联选择器需要的格式
      if (result.code === 200) {
        industryOptions.value = convertToTreeData(result.data)
      } else {
        throw new Error(result.message || '获取国民经济行业分类失败')
      }
    } else {
      // 使用分类管理名录（默认）
      const fetchResponse = await fetch('/api/industries')
      result = await fetchResponse.json()

      if (result.code === 200) {
        industryOptions.value = result.data
      } else {
        throw new Error(result.message || '获取行业数据失败')
      }
    }
  } catch (error) {
    console.error('加载行业数据失败:', error)
    ElMessage.error('加载行业数据失败')
  }
}

// 获取节点下的所有叶子节点ID
const getAllLeafIds = (node) => {
  const leafIds = []

  const traverse = (currentNode) => {
    if (!currentNode.children || currentNode.children.length === 0) {
      // 叶子节点
      leafIds.push(currentNode.id)
    } else {
      // 父节点，遍历子节点
      currentNode.children.forEach(child => traverse(child))
    }
  }

  traverse(node)
  return leafIds
}

// 在选项树中查找节点
const findNodeById = (options, targetId) => {
  for (const option of options) {
    if (option.id === targetId) {
      return option
    }
    if (option.children && option.children.length > 0) {
      const found = findNodeById(option.children, targetId)
      if (found) return found
    }
  }
  return null
}

// 处理选择变化
const handleChange = (value) => {
  if (!value || value.length === 0) {
    selectedIndustries.value = []
    emit('update:modelValue', selectedIndustries.value)
    return
  }

  let finalSelection = []

  // 处理每个选中的值
  value.forEach(selectedId => {
    // 检查是否是分类ID（虚拟父级）
    if (selectedId.toString().startsWith('category_')) {
      // 找到对应的分类节点
      const categoryNode = findNodeById(industryOptions.value, selectedId)

      if (categoryNode && categoryNode.children && categoryNode.children.length > 0) {
        // 添加所有子级的真实ID
        const leafIds = getAllLeafIds(categoryNode)

        leafIds.forEach(leafId => {
          if (!finalSelection.includes(leafId)) {
            finalSelection.push(leafId)
          }
        })
      }
    } else {
      // 这是真实的行业ID，直接添加
      if (!finalSelection.includes(selectedId)) {
        finalSelection.push(selectedId)
      }
    }
  })

  selectedIndustries.value = finalSelection
  emit('update:modelValue', selectedIndustries.value)
}

// 移除行业
const removeIndustry = (index) => {
  selectedIndustries.value.splice(index, 1)
  emit('update:modelValue', selectedIndustries.value)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedIndustries.value = newValue || []
}, { immediate: true })

// 初始化
onMounted(() => {
  loadIndustries()
})
</script>

<style scoped>
.industry-selector {
  width: 100%;
}

.selected-industries {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.selected-title {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

:deep(.el-cascader) {
  width: 100%;
}

:deep(.el-cascader__tags) {
  max-height: 120px;
  overflow-y: auto;
}

:deep(.el-tag--info) {
  background-color: #f0f9ff;
  border-color: #b3d8ff;
  color: #409eff;
}

:deep(.el-tag .el-icon-close) {
  color: #409eff;
}

:deep(.el-tag .el-icon-close:hover) {
  background-color: #409eff;
  color: white;
}

/* 确保级联选择器下拉面板有足够高的层级，比对话框更高 */
:global(.industry-selector-popper) {
  z-index: 9999 !important;
}

:global(.industry-selector-popper .el-cascader-panel) {
  z-index: 9999 !important;
}

:global(.el-cascader__dropdown.industry-selector-popper) {
  z-index: 9999 !important;
}

/* 更强力的选择器覆盖 */
:global(.el-popper.industry-selector-popper) {
  z-index: 9999 !important;
}

:global(.el-cascader__dropdown) {
  z-index: 9999 !important;
}

/* 针对所有级联选择器下拉面板 */
:global(.el-cascader-panel) {
  z-index: 9999 !important;
}
</style>
