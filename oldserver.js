const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const csv = require('csv-parser');
const multer = require('multer');

const app = express();
const PORT = process.env.PORT || 3001;
const DB_PATH = path.join(__dirname, 'data', 'ltwiki.db');

// 中间件
app.use(cors());
app.use(express.json());
// 删除静态文件服务，因为前端是独立的Vue项目

// 数据库连接
let db;

// 初始化数据库
function initDatabase() {
  return new Promise((resolve, reject) => {
    db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('数据库连接失败:', err);
        reject(err);
      } else {
        console.log('数据库连接成功');
        createTables().then(resolve).catch(reject);
      }
    });
  });
}

// 创建数据表
function createTables() {
  return new Promise((resolve, reject) => {
    const tables = [
      // 区域表
      `CREATE TABLE IF NOT EXISTS regions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20),
        region_type VARCHAR(20) DEFAULT 'normal',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 园区表
      `CREATE TABLE IF NOT EXISTS parks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(200) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 区域园区关联表
      `CREATE TABLE IF NOT EXISTS region_park_relations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        region_id INTEGER,
        park_id INTEGER,
        is_primary BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (region_id) REFERENCES regions(id),
        FOREIGN KEY (park_id) REFERENCES parks(id)
      )`,
      
      // 行业表
      `CREATE TABLE IF NOT EXISTS industries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        parent_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES industries(id)
      )`,
      
      // 行业关键词表
      `CREATE TABLE IF NOT EXISTS industry_keywords (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        industry_id INTEGER,
        keyword VARCHAR(100),
        FOREIGN KEY (industry_id) REFERENCES industries(id)
      )`,
      
      // 通用标签表
      `CREATE TABLE IF NOT EXISTS general_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag_name VARCHAR(100),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 政策类型表
      `CREATE TABLE IF NOT EXISTS policy_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 政策主表
      `CREATE TABLE IF NOT EXISTS policies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(500) NOT NULL,
        policy_type_id INTEGER NOT NULL,
        publish_org VARCHAR(200),
        publish_date DATE,
        source_file_url TEXT,
        summary TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (policy_type_id) REFERENCES policy_types(id)
      )`,
      
      // 管控单元表（三线一单专用）
      `CREATE TABLE IF NOT EXISTS control_units (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        policy_id INTEGER NOT NULL,
        unit_code VARCHAR(50) NOT NULL,
        unit_name VARCHAR(500) NOT NULL,
        region_id INTEGER,
        park_id INTEGER,
        unit_type VARCHAR(50),
        spatial_constraints TEXT,
        pollution_control TEXT,
        risk_prevention TEXT,
        resource_efficiency TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (policy_id) REFERENCES policies(id),
        FOREIGN KEY (region_id) REFERENCES regions(id),
        FOREIGN KEY (park_id) REFERENCES parks(id)
      )`,
      
      // 通用政策条款表
      `CREATE TABLE IF NOT EXISTS policy_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        policy_id INTEGER NOT NULL,
        region_id INTEGER,
        park_id INTEGER,
        industry_id INTEGER,
        rule_content TEXT NOT NULL,
        rule_type VARCHAR(100),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (policy_id) REFERENCES policies(id),
        FOREIGN KEY (region_id) REFERENCES regions(id),
        FOREIGN KEY (park_id) REFERENCES parks(id),
        FOREIGN KEY (industry_id) REFERENCES industries(id)
      )`,
      
      // 政策标签关联表
      `CREATE TABLE IF NOT EXISTS policy_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        policy_id INTEGER,
        tag_id INTEGER,
        FOREIGN KEY (policy_id) REFERENCES policies(id),
        FOREIGN KEY (tag_id) REFERENCES general_tags(id)
      )`
    ];

    let completed = 0;
    tables.forEach((sql, index) => {
      db.run(sql, (err) => {
        if (err) {
          console.error(`创建表失败 ${index}:`, err);
          reject(err);
        } else {
          completed++;
          if (completed === tables.length) {
            console.log('所有数据表创建成功');
            initializeBasicData().then(resolve).catch(reject);
          }
        }
      });
    });
  });
}

// 初始化基础数据
function initializeBasicData() {
  return new Promise((resolve, reject) => {
    // 检查是否已有数据
    db.get("SELECT COUNT(*) as count FROM regions", (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (row.count > 0) {
        console.log('基础数据已存在，跳过初始化');
        resolve();
        return;
      }
      
      console.log('开始初始化基础数据...');
      
      // 插入区域数据
      const regions = [
        { name: '红旗区', code: '410702', type: 'normal' },
        { name: '卫滨区', code: '410703', type: 'normal' },
        { name: '凤泉区', code: '410704', type: 'normal' },
        { name: '牧野区', code: '410711', type: 'normal' },
        { name: '新乡县', code: '410721', type: 'normal' },
        { name: '获嘉县', code: '410724', type: 'normal' },
        { name: '原阳县', code: '410725', type: 'normal' },
        { name: '延津县', code: '410726', type: 'normal' },
        { name: '封丘县', code: '410727', type: 'normal' },
        { name: '卫辉市', code: '410781', type: 'normal' },
        { name: '辉县市', code: '410782', type: 'normal' },
        { name: '长垣市', code: '410783', type: 'normal' },
        { name: '经开区', code: 'JKQU', type: 'special' },
        { name: '高新区', code: 'GXQU', type: 'special' }
      ];
      
      // 园区数据
      const parks = [
        '新乡红旗区先进制造业开发区',
        '新乡工业产业集聚区',
        '新乡高新技术产业开发区',
        '新乡经济技术开发区',
        '新乡电源产业开发区',
        '新乡经济开发区',
        '获嘉县先进制造业开发区',
        '原阳县先进制造业开发区',
        '新乡市平原现代服务业开发区',
        '延津县先进制造业开发区',
        '封丘县先进制造业开发区',
        '卫辉市先进制造业开发区',
        '辉县经济技术开发区',
        '长垣经济技术开发区'
      ];
      
      // 政策类型数据
      const policyTypes = [
        { name: '三线一单', description: '生态保护红线、环境质量底线、资源利用上线和生态环境准入清单' },
        { name: '行业审批原则', description: '各行业建设项目环境影响评价审批原则' },
        { name: '园区准入要求', description: '产业园区准入负面清单和管理要求' },
        { name: '行业规范标准', description: '行业技术规范和排放标准' },
        { name: '负面清单', description: '产业结构调整指导目录等负面清单' }
      ];
      
      // 行业数据
      const industries = [
        { name: '制造业', parent_id: null },
        { name: '服务业', parent_id: null },
        { name: '其他', parent_id: null }
      ];
      
      // 通用标签数据
      const generalTags = [
        '环评', '节能评估', '安全评价', '职业卫生', '消防审批', '规划许可', '土地审批'
      ];
      
      // 批量插入数据
      let completed = 0;
      const totalTasks = 6; // 增加到6个任务
      
      // 插入区域
      const regionStmt = db.prepare("INSERT INTO regions (name, code, region_type) VALUES (?, ?, ?)");
      regions.forEach(region => {
        regionStmt.run(region.name, region.code, region.type);
      });
      regionStmt.finalize(() => {
        completed++;
        if (completed === totalTasks) resolve();
      });
      
      // 插入园区
      const parkStmt = db.prepare("INSERT INTO parks (name) VALUES (?)");
      parks.forEach(park => {
        parkStmt.run(park);
      });
      parkStmt.finalize(() => {
        completed++;
        if (completed === totalTasks) resolve();
      });
      
      // 插入政策类型
      const policyTypeStmt = db.prepare("INSERT INTO policy_types (name, description) VALUES (?, ?)");
      policyTypes.forEach(type => {
        policyTypeStmt.run(type.name, type.description);
      });
      policyTypeStmt.finalize(() => {
        completed++;
        if (completed === totalTasks) resolve();
      });
      
      // 插入行业
      const industryStmt = db.prepare("INSERT INTO industries (name, parent_id) VALUES (?, ?)");
      industries.forEach(industry => {
        industryStmt.run(industry.name, industry.parent_id);
      });
      industryStmt.finalize(() => {
        // 插入详细行业分类
        setTimeout(() => {
          initDetailedIndustries();
        }, 100);
        completed++;
        if (completed === totalTasks) resolve();
      });
      
      // 插入通用标签
      const tagStmt = db.prepare("INSERT INTO general_tags (tag_name) VALUES (?)");
      generalTags.forEach(tag => {
        tagStmt.run(tag);
      });
      tagStmt.finalize(() => {
        completed++;
        if (completed === totalTasks) resolve();
      });
      
      // 初始化详细行业数据（新增任务）
      function initDetailedIndustries() {
        // 获取父级行业ID
        db.all("SELECT id, name FROM industries WHERE parent_id IS NULL", (err, parentIndustries) => {
          if (err) {
            console.error('获取父级行业失败:', err);
            completed++;
            if (completed === totalTasks) resolve();
            return;
          }
          
          const manufacturingId = parentIndustries.find(p => p.name === '制造业')?.id;
          const serviceId = parentIndustries.find(p => p.name === '服务业')?.id;
          const otherId = parentIndustries.find(p => p.name === '其他')?.id;
          
          // 详细制造业分类
          const detailedIndustries = [
            // 制造业子分类
            { name: '化工', parent_id: manufacturingId },
            { name: '机械制造', parent_id: manufacturingId },
            { name: '电子信息', parent_id: manufacturingId },
            { name: '纺织服装', parent_id: manufacturingId },
            { name: '食品加工', parent_id: manufacturingId },
            { name: '医药制造', parent_id: manufacturingId },
            { name: '建材', parent_id: manufacturingId },
            { name: '冶金', parent_id: manufacturingId },
            // 服务业子分类
            { name: '物流仓储', parent_id: serviceId },
            { name: '商贸服务', parent_id: serviceId },
            { name: '金融服务', parent_id: serviceId },
            { name: '信息服务', parent_id: serviceId },
            // 其他分类
            { name: '农业', parent_id: otherId },
            { name: '建筑业', parent_id: otherId },
            { name: '采矿业', parent_id: otherId }
          ];
          
          const detailStmt = db.prepare("INSERT INTO industries (name, parent_id) VALUES (?, ?)");
          detailedIndustries.forEach(industry => {
            detailStmt.run(industry.name, industry.parent_id);
          });
          detailStmt.finalize(() => {
            // 插入更详细的三级分类
            setTimeout(() => {
              initThirdLevelIndustries();
            }, 100);
          });
        });
      }
      
      // 初始化三级行业分类和关键词
      function initThirdLevelIndustries() {
        db.all("SELECT id, name FROM industries WHERE parent_id IS NOT NULL", (err, secondLevelIndustries) => {
          if (err) {
            console.error('获取二级行业失败:', err);
            completed++;
            if (completed === totalTasks) resolve();
            return;
          }
          
          const chemicalId = secondLevelIndustries.find(p => p.name === '化工')?.id;
          const mechanicalId = secondLevelIndustries.find(p => p.name === '机械制造')?.id;
          const electronicId = secondLevelIndustries.find(p => p.name === '电子信息')?.id;
          const textileId = secondLevelIndustries.find(p => p.name === '纺织服装')?.id;
          const foodId = secondLevelIndustries.find(p => p.name === '食品加工')?.id;
          
          // 三级行业分类
          const thirdLevelIndustries = [
            // 化工细分
            { name: '电镀', parent_id: chemicalId },
            { name: '涂料生产', parent_id: chemicalId },
            { name: '化学原料', parent_id: chemicalId },
            { name: '塑料制品', parent_id: chemicalId },
            // 机械制造细分
            { name: '汽车制造', parent_id: mechanicalId },
            { name: '装备制造', parent_id: mechanicalId },
            { name: '金属加工', parent_id: mechanicalId },
            // 电子信息细分
            { name: '电子元器件', parent_id: electronicId },
            { name: '通信设备', parent_id: electronicId },
            // 纺织服装细分
            { name: '服装制造', parent_id: textileId },
            { name: '纺织品', parent_id: textileId },
            // 食品加工细分
            { name: '食品制造', parent_id: foodId },
            { name: '饮料制造', parent_id: foodId }
          ];
          
          const thirdStmt = db.prepare("INSERT INTO industries (name, parent_id) VALUES (?, ?)");
          thirdLevelIndustries.forEach(industry => {
            thirdStmt.run(industry.name, industry.parent_id);
          });
          thirdStmt.finalize(() => {
            // 插入行业关键词
            setTimeout(() => {
              initIndustryKeywords();
            }, 100);
          });
        });
      }
      
      // 初始化行业关键词
      function initIndustryKeywords() {
        db.all("SELECT id, name FROM industries", (err, allIndustries) => {
          if (err) {
            console.error('获取所有行业失败:', err);
            completed++;
            if (completed === totalTasks) resolve();
            return;
          }
          
          // 行业关键词映射
          const industryKeywords = [
            // 电镀行业
            { industry: '电镀', keywords: ['VOCs排放', '重金属污染', '废水处理', '电镀废液', '六价铬', '镍排放'] },
            // 涂料生产
            { industry: '涂料生产', keywords: ['VOCs排放', '有机溶剂', '苯系物', '甲醛', '废气处理'] },
            // 化学原料
            { industry: '化学原料', keywords: ['危险化学品', '废水处理', '有毒有害物质', '环境风险', '应急预案'] },
            // 汽车制造
            { industry: '汽车制造', keywords: ['喷漆废气', 'VOCs排放', '废水处理', '噪声控制', '固废处理'] },
            // 装备制造
            { industry: '装备制造', keywords: ['机加工废水', '切削液', '噪声控制', '粉尘处理', '固废处理'] },
            // 食品制造
            { industry: '食品制造', keywords: ['废水处理', 'COD排放', '异味控制', '食品安全', '清洁生产'] },
            // 服装制造
            { industry: '服装制造', keywords: ['印染废水', '染料污染', 'COD排放', '色度处理', '节水减排'] },
            // 电子元器件
            { industry: '电子元器件', keywords: ['清洗废水', '有机废气', '重金属', '危险废物', '洁净生产'] }
          ];
          
          const keywordStmt = db.prepare("INSERT INTO industry_keywords (industry_id, keyword) VALUES (?, ?)");
          
          industryKeywords.forEach(item => {
            const industry = allIndustries.find(ind => ind.name === item.industry);
            if (industry) {
              item.keywords.forEach(keyword => {
                keywordStmt.run(industry.id, keyword);
              });
            }
          });
          
          keywordStmt.finalize(() => {
            console.log('详细行业数据和关键词初始化完成');
            completed++;
            if (completed === totalTasks) resolve();
          });
        });
      }
      
      console.log('基础数据初始化完成');
    });
  });
}

// API 路由

// 获取区域列表
app.get('/api/regions', (req, res) => {
  db.all("SELECT * FROM regions ORDER BY id", (err, rows) => {
    if (err) {
      res.status(500).json({ code: 500, message: '获取区域列表失败', error: err.message });
    } else {
      res.json({ code: 200, data: rows });
    }
  });
});

// 获取园区列表（根据地区筛选）
app.get('/api/parks', (req, res) => {
  const { region_id } = req.query;
  
  if (region_id) {
    // 根据区域获取园区
    const sql = `
      SELECT DISTINCT p.* FROM parks p
      JOIN region_park_relations rpr ON p.id = rpr.park_id
      WHERE rpr.region_id = ?
      ORDER BY p.name
    `;
    db.all(sql, [region_id], (err, rows) => {
      if (err) {
        res.status(500).json({ code: 500, message: '获取园区列表失败', error: err.message });
      } else {
        res.json({ code: 200, data: rows });
      }
    });
  } else {
    // 获取所有园区
    db.all("SELECT * FROM parks ORDER BY name", (err, rows) => {
      if (err) {
        res.status(500).json({ code: 500, message: '获取园区列表失败', error: err.message });
      } else {
        res.json({ code: 200, data: rows });
      }
    });
  }
});

// 获取管控单元列表（根据地区筛选，排除园区）
app.get('/api/control-units', (req, res) => {
  const { region_id } = req.query;
  
  if (!region_id) {
    res.status(400).json({ code: 400, message: '缺少region_id参数' });
    return;
  }
  
  // 获取该地区非园区的管控单元
  const sql = `
    SELECT * FROM control_units 
    WHERE region_id = ? AND park_id IS NULL
    ORDER BY unit_name
  `;
  
  db.all(sql, [region_id], (err, rows) => {
    if (err) {
      res.status(500).json({ code: 500, message: '获取管控单元失败', error: err.message });
    } else {
      res.json({ code: 200, data: rows });
    }
  });
});

// 获取行业列表（基于分类管理名录）
app.get('/api/industries', (req, res) => {
    const query = `
    SELECT id, code, name, category, category_order, item_number
        FROM industries 
        ORDER BY category_order, item_number, code
    `;
    
    db.all(query, [], (err, rows) => {
        if (err) {
            console.error('获取行业列表失败:', err);
      res.status(500).json({ code: 500, message: '获取行业列表失败', error: err.message });
            return;
        }
        
    // 按大类分组，转换为级联选择器需要的格式
        const grouped = {};
        rows.forEach(row => {
            if (!grouped[row.category]) {
                grouped[row.category] = {
          id: `category_${row.category}`,
          name: row.category,
          children: []
                };
            }
      
      // 如果有具体的行业代码，添加为子项
      if (row.code) {
        grouped[row.category].children.push({
          id: row.id,
          name: `${row.name}${row.code}`,
                code: row.code,
          category: row.category
        });
      } else {
        // 如果没有代码，直接作为大类下的项目
        grouped[row.category].children.push({
          id: row.id,
                name: row.name,
          category: row.category
            });
      }
        });
        
    // 转换为数组并排序
    const result = Object.values(grouped).sort((a, b) => {
      const aOrder = rows.find(r => r.category === a.name.replace('category_', ''))?.category_order || 0;
      const bOrder = rows.find(r => r.category === b.name.replace('category_', ''))?.category_order || 0;
      return aOrder - bOrder;
    });
    
    res.json({ code: 200, data: result });
    });
});

// 新的、统一的国民经济行业分类详情接口
app.get('/api/industry-classification/detail', (req, res) => {
    const { name } = req.query;
    if (!name) {
        return res.status(400).json({ code: 400, message: '行业名称不能为空' });
    }

    // 智能清理名称：移除末尾的注释括号和代码
    // 例如："非金属废料和碎屑加工处理（...）422" -> "非金属废料和碎屑加工处理"
    // "羽毛（绒）加工及制品制造194*" -> "羽毛（绒）加工及制品制造"
    const pureName = name.replace(/（[^）]+）\s*\d*$/, '').replace(/[\d\*]+$/, '').trim();

    // 1. 查找最匹配的主分类（按层级和名称长度排序，确保最精确）
    const findMainQuery = `
        SELECT * FROM industry_classification
        WHERE primary_name LIKE ?
        ORDER BY level ASC, length(primary_name) ASC
        LIMIT 1
    `;

    db.get(findMainQuery, [`%${pureName}%`], (err, mainClassification) => {
        if (err) {
            console.error('查找主分类失败:', err);
            return res.status(500).json({ code: 500, message: '查找主分类失败' });
        }
        if (!mainClassification) {
            return res.status(404).json({ code: 404, message: `未找到与"${pureName}"相关的行业` });
        }

        // 2. 使用递归CTE查找所有后代
        const descendantsQuery = `
            WITH RECURSIVE classification_tree AS (
              SELECT * FROM industry_classification WHERE code = ?
              UNION ALL
              SELECT t.*
              FROM industry_classification t
              JOIN classification_tree ct ON t.parent_code = ct.code
            )
            SELECT * FROM classification_tree WHERE code != ?;
        `;

        db.all(descendantsQuery, [mainClassification.code, mainClassification.code], (err, descendants) => {
            if (err) {
                console.error('查找子分类失败:', err);
                return res.status(500).json({ code: 500, message: '查找子分类失败' });
            }

            res.json({
                code: 200,
                data: {
                    main: mainClassification,
                    descendants: descendants || []
                }
            });
        });
    });
});

// 搜索国民经济行业分类
app.get('/api/industry-classification/search', (req, res) => {
    const { keyword } = req.query;

    if (!keyword) {
        return res.status(400).json({ code: 400, message: '搜索关键词不能为空' });
    }

    // 判断是代码还是文本
    const isCode = /^\d+$/.test(keyword);

    let query;
    const params = [keyword];

    if (isCode) {
        // 按代码精准匹配
        query = `
            SELECT code, level, level_name, primary_name, detailed_description, full_path_description
            FROM industry_classification 
            WHERE code = ?
        `;
    } else {
        // 按名称精准匹配
        query = `
            SELECT code, level, level_name, primary_name, detailed_description, full_path_description
            FROM industry_classification 
            WHERE primary_name = ?
        `;
    }

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('搜索行业分类失败:', err);
            return res.status(500).json({ code: 500, message: '搜索行业分类失败', error: err.message });
        }
        res.json({ code: 200, data: rows });
    });
});

// 获取特定行业ID的环境影响评价要求
app.get('/api/industries/:industryId/requirements', (req, res) => {
    const { industryId } = req.params;

    const industryQuery = `
        SELECT 
            id, code, name, category,
            report_book, report_table, registration_form, sensitive_area_meaning
        FROM industries 
        WHERE id = ?
    `;
    
    db.get(industryQuery, [industryId], (err, industry) => {
        if (err) {
            console.error('获取行业要求失败:', err);
            return res.status(500).json({ code: 500, message: '获取行业要求失败', error: err.message });
        }
        
        if (!industry) {
            return res.status(404).json({ code: 404, message: '未找到该行业' });
        }
        
        // 将单行结果适配前端期望的格式
        const result = {
            industry: {
                id: industry.id,
                code: industry.code,
                name: industry.name,
                category: industry.category,
            },
            tableHeader: {
                title: '建设项目环境影响评价分类管理名录（2021年版）',
                columns: [
                    '项目类别',
                    '报告书',
                    '报告表',
                    '登记表',
                    '本表中环境敏感区含义'
                ]
            },
            requirements: [{
                projectTypes: industry.name, // 直接使用行业名称
                reportBook: industry.report_book,
                reportTable: industry.report_table,
                registrationForm: industry.registration_form,
                sensitiveAreaMeaning: industry.sensitive_area_meaning
            }]
        };
        
        res.json({ code: 200, data: result });
    });
});

// 获取行业关键词
app.get('/api/industry-keywords', (req, res) => {
  const { industry_id } = req.query;
  
  if (!industry_id) {
    res.status(400).json({ code: 400, message: '缺少industry_id参数' });
    return;
  }
  
  db.all("SELECT keyword FROM industry_keywords WHERE industry_id = ?", [industry_id], (err, rows) => {
    if (err) {
      res.status(500).json({ code: 500, message: '获取行业关键词失败', error: err.message });
    } else {
      res.json({ code: 200, data: rows.map(row => row.keyword) });
    }
  });
});

// 获取通用标签
app.get('/api/general-tags', (req, res) => {
  db.all("SELECT * FROM general_tags ORDER BY tag_name", (err, rows) => {
    if (err) {
      res.status(500).json({ code: 500, message: '获取通用标签失败', error: err.message });
    } else {
      res.json({ code: 200, data: rows });
    }
  });
});

// 必选项查询（精准匹配）
app.get('/api/search/required', (req, res) => {
  const { region_id, in_park, park_id, control_unit_id, industry_id } = req.query;
  
  if (!region_id || in_park === undefined || !industry_id) {
    res.status(400).json({ code: 400, message: '缺少必要参数' });
    return;
  }
  
  const isInPark = in_park === 'true';
  
  if (isInPark && !park_id) {
    res.status(400).json({ code: 400, message: '选择在园区时必须提供park_id' });
    return;
  }
  
  if (!isInPark && !control_unit_id) {
    res.status(400).json({ code: 400, message: '选择不在园区时必须提供control_unit_id' });
    return;
  }
  
  const result = {
    sanxianyidan: [],
    industry_policies: [],
    park_policies: []
  };
  
  let completed = 0;
  const totalQueries = 3;
  
  // 查询三线一单管控要求
  if (isInPark) {
    // 园区管控单元
    const sql = `
      SELECT * FROM control_units 
      WHERE park_id = ? AND region_id = ?
    `;
    db.all(sql, [park_id, region_id], (err, rows) => {
      if (!err) result.sanxianyidan = rows;
      completed++;
      if (completed === totalQueries) res.json({ code: 200, data: result });
    });
  } else {
    // 非园区管控单元
    const sql = `
      SELECT * FROM control_units 
      WHERE id = ?
    `;
    db.get(sql, [control_unit_id], (err, row) => {
      if (!err && row) result.sanxianyidan = [row];
      completed++;
      if (completed === totalQueries) res.json({ code: 200, data: result });
    });
  }
  
  // 查询行业政策（暂时返回空数组，后续通过管理后台录入）
  result.industry_policies = [];
  completed++;
  if (completed === totalQueries) res.json({ code: 200, data: result });
  
  // 查询园区政策（暂时返回空数组，后续通过管理后台录入）
  result.park_policies = [];
  completed++;
  if (completed === totalQueries) res.json({ code: 200, data: result });
});

// 可选项查询（关键词匹配）
app.get('/api/search/optional', (req, res) => {
  const { industry_keywords, general_tags } = req.query;
  
  // 暂时返回空数组，后续通过管理后台录入政策后实现关键词匹配
  res.json({ code: 200, data: [] });
});

// 三线一单查询
app.post('/api/sanxianyidan', (req, res) => {
  const { location, unitType } = req.body;
  
  if (!location) {
    res.status(400).json({ code: 400, message: '请输入项目位置' });
    return;
  }
  
  // 构建查询SQL
  let sql = `
    SELECT cu.*, r.name as county 
    FROM control_units cu
    LEFT JOIN regions r ON cu.region_id = r.id
    WHERE 1=1
  `;
  const params = [];
  
  // 根据位置模糊查询
  if (location) {
    sql += ` AND (cu.unit_name LIKE ? OR r.name LIKE ?)`;
    params.push(`%${location}%`, `%${location}%`);
  }
  
  // 根据管控单元类型筛选
  if (unitType) {
    sql += ` AND cu.unit_type = ?`;
    params.push(unitType);
  }
  
  sql += ` ORDER BY cu.unit_name`;
  
  db.all(sql, params, (err, rows) => {
    if (err) {
      console.error('三线一单查询失败:', err);
      res.status(500).json({ code: 500, message: '查询失败', error: err.message });
    } else {
      res.json({ code: 200, data: rows });
    }
  });
});

// 启动服务器前先初始化数据库
initDatabase().then(() => {
  // 检查并导入三线一单数据
  const csvPath = path.join(__dirname, '三线一单.csv');
  if (fs.existsSync(csvPath)) {
    console.log('发现三线一单.csv文件，开始导入...');
    importSanxianyidanData(csvPath);
  }
  
  app.listen(PORT, () => {
    console.log(`后端服务器运行在 http://localhost:${PORT}`);
    console.log('前端请通过 http://localhost:3000 访问（Vite代理）');
  });
}).catch(err => {
  console.error('数据库初始化失败:', err);
  process.exit(1);
});

// 导入三线一单数据
function importSanxianyidanData(csvPath) {
  // 首先检查是否已有三线一单数据
  db.get("SELECT COUNT(*) as count FROM control_units", (err, row) => {
    if (err || row.count > 0) {
      console.log('三线一单数据已存在，跳过导入');
      return;
    }
    
    console.log('开始导入三线一单数据...');
    
    // 先创建三线一单政策记录
    db.run(
      "INSERT INTO policies (title, policy_type_id, publish_org, summary) VALUES (?, ?, ?, ?)",
      ['新乡市"三线一单"生态环境准入清单', 1, '新乡市生态环境局', '新乡市生态保护红线、环境质量底线、资源利用上线和生态环境准入清单'],
      function(err) {
        if (err) {
          console.error('创建三线一单政策记录失败:', err);
          return;
        }
        
        const policyId = this.lastID;
        
        // 手动解析CSV文件
        try {
          const content = fs.readFileSync(csvPath, 'utf8');
          const lines = content.split('\n');
          
          console.log(`CSV文件总行数: ${lines.length}`);
          
          const controlUnits = [];
          let currentRecord = null;
          let currentField = '';
          let insideQuotes = false;
          
          // 从第4行开始处理（跳过前3行标题）
          for (let i = 3; i < lines.length; i++) {
            let line = lines[i].trim();
            if (!line) continue;
            
            // 检查是否是新记录的开始（以ZH开头的编码）
            if (line.match(/^ZH\d+/)) {
              // 保存上一条记录
              if (currentRecord && currentRecord.unit_code && currentRecord.unit_name) {
                controlUnits.push(currentRecord);
              }
              
              // 开始新记录
              const parts = [];
              let currentPart = '';
              let inQuotes = false;
              
              for (let j = 0; j < line.length; j++) {
                const char = line[j];
                if (char === '"') {
                  inQuotes = !inQuotes;
                  currentPart += char;
                } else if (char === ',' && !inQuotes) {
                  parts.push(currentPart.replace(/^"|"$/g, ''));
                  currentPart = '';
                } else {
                  currentPart += char;
                }
              }
              if (currentPart) {
                parts.push(currentPart.replace(/^"|"$/g, ''));
              }
              
              // 解析字段
              currentRecord = {
                policy_id: policyId,
                unit_code: parts[0] || '',
                unit_name: parts[1] || '',
                province: parts[2] || '',
                city: parts[3] || '',
                county: parts[4] || '',
                unit_type: parts[5] || '',
                spatial_constraints: parts[6] || '',
                pollution_control: '',
                risk_prevention: '',
                resource_efficiency: ''
              };
              
              // 如果第7列不完整，准备接收后续行
              if (parts[6] && parts[6].includes('、') && !parts[6].includes('。')) {
                currentField = 'spatial_constraints';
                insideQuotes = true;
              }
            } else if (currentRecord && insideQuotes) {
              // 继续处理多行字段
              if (currentField === 'spatial_constraints') {
                currentRecord.spatial_constraints += '\\n' + line.replace(/^"|"$/, '');
                if (line.includes('。"') || line.endsWith('。')) {
                  currentField = 'pollution_control';
                }
              } else if (currentField === 'pollution_control') {
                if (line.includes('、') && !currentRecord.pollution_control) {
                  currentRecord.pollution_control = line.replace(/^"|"$/, '');
                } else {
                  currentRecord.pollution_control += '\\n' + line.replace(/^"|"$/, '');
                }
                if (line.includes('。"') || line.endsWith('。')) {
                  currentField = 'risk_prevention';
                }
              } else if (currentField === 'risk_prevention') {
                if (line.includes('、') && !currentRecord.risk_prevention) {
                  currentRecord.risk_prevention = line.replace(/^"|"$/, '');
                } else {
                  currentRecord.risk_prevention += '\\n' + line.replace(/^"|"$/, '');
                }
                if (line.includes('。"') || line.endsWith('。')) {
                  currentField = 'resource_efficiency';
                }
              } else if (currentField === 'resource_efficiency') {
                if (line.includes('、') && !currentRecord.resource_efficiency) {
                  currentRecord.resource_efficiency = line.replace(/^"|"$/, '');
                } else {
                  currentRecord.resource_efficiency += '\\n' + line.replace(/^"|"$/, '');
                }
                if (line.includes('。"') || line.endsWith('。') || line === '/') {
                  insideQuotes = false;
                  currentField = '';
                }
              }
            }
          }
          
          // 保存最后一条记录
          if (currentRecord && currentRecord.unit_code && currentRecord.unit_name) {
            controlUnits.push(currentRecord);
          }
          
          console.log(`解析到 ${controlUnits.length} 条管控单元数据`);
          
          if (controlUnits.length === 0) {
            console.log('没有解析到有效数据，使用简化解析方法...');
            // 使用简化方法重新解析
            parseSimpleCSV();
            return;
          }
          
          // 批量插入数据
          insertControlUnits(controlUnits, policyId);
          
        } catch (error) {
          console.error('CSV文件读取失败:', error);
          console.log('尝试使用简化解析方法...');
          parseSimpleCSV();
        }
        
        // 简化解析方法
        function parseSimpleCSV() {
          const content = fs.readFileSync(csvPath, 'utf8');
          const lines = content.split('\\n');
          const controlUnits = [];
          
          for (let i = 3; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line || !line.startsWith('ZH')) continue;
            
            // 简单按逗号分割，只取前6个字段
            const parts = line.split(',');
            if (parts.length >= 6) {
              controlUnits.push({
                policy_id: policyId,
                unit_code: parts[0].replace(/"/g, ''),
                unit_name: parts[1].replace(/"/g, ''),
                province: parts[2].replace(/"/g, ''),
                city: parts[3].replace(/"/g, ''),
                county: parts[4].replace(/"/g, ''),
                unit_type: parts[5].replace(/"/g, ''),
                spatial_constraints: '详见原始文件',
                pollution_control: '详见原始文件',
                risk_prevention: '详见原始文件',
                resource_efficiency: '详见原始文件'
              });
            }
          }
          
          console.log(`简化解析得到 ${controlUnits.length} 条记录`);
          insertControlUnits(controlUnits, policyId);
        }
        
        // 插入管控单元数据
        function insertControlUnits(controlUnits, policyId) {
          if (controlUnits.length === 0) {
            console.log('没有有效的管控单元数据');
            return;
          }
          
          const stmt = db.prepare(`
            INSERT INTO control_units (
              policy_id, unit_code, unit_name, region_id, park_id, unit_type,
              spatial_constraints, pollution_control, risk_prevention, resource_efficiency
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);
          
          let processed = 0;
          controlUnits.forEach(unit => {
            // 根据县名匹配region_id
            db.get("SELECT id FROM regions WHERE name = ?", [unit.county], (err, region) => {
              let regionId = region ? region.id : null;
              
              // 根据管控单元名称判断是否属于园区
              let parkId = null;
              const unitNameLower = unit.unit_name.toLowerCase();
              
              if (unitNameLower.includes('开发区') || 
                  unitNameLower.includes('产业集聚区') || 
                  unitNameLower.includes('高新') ||
                  unitNameLower.includes('经开') ||
                  unitNameLower.includes('工业园') ||
                  unitNameLower.includes('科技园')) {
                
                // 尝试匹配具体园区
                const parkKeywords = [
                  unit.unit_name.replace('新乡', '').trim(),
                  unit.unit_name.trim()
                ];
                
                for (const keyword of parkKeywords) {
                  db.get("SELECT id FROM parks WHERE name LIKE ?", [`%${keyword}%`], (err, park) => {
                    if (park) parkId = park.id;
                  });
                }
              }
              
              stmt.run(
                unit.policy_id, unit.unit_code, unit.unit_name, regionId, parkId,
                unit.unit_type, unit.spatial_constraints, unit.pollution_control,
                unit.risk_prevention, unit.resource_efficiency
              );
              
              processed++;
              if (processed === controlUnits.length) {
                stmt.finalize();
                console.log('三线一单数据导入完成');
                
                // 建立区域园区关联关系
                createRegionParkRelations();
              }
            });
          });
        }
      }
    );
  });
}

// 建立区域园区关联关系
function createRegionParkRelations() {
  console.log('开始建立区域园区关联关系...');
  
  // 预定义的关联关系
  const relations = [
    // 经开区包含的园区
    { region: '经开区', parks: ['新乡工业产业集聚区', '新乡高新技术产业开发区', '新乡经济技术开发区'] },
    // 高新区
    { region: '高新区', parks: ['新乡高新技术产业开发区'] },
    // 红旗区包含的园区
    { region: '红旗区', parks: ['新乡红旗区先进制造业开发区', '新乡工业产业集聚区', '新乡高新技术产业开发区', '新乡经济技术开发区'] },
    // 其他区域的园区
    { region: '牧野区', parks: ['新乡电源产业开发区'] },
    { region: '新乡县', parks: ['新乡经济开发区', '新乡高新技术产业开发区'] },
    { region: '延津县', parks: ['延津县先进制造业开发区', '新乡工业产业集聚区', '新乡经济技术开发区'] },
    { region: '获嘉县', parks: ['获嘉县先进制造业开发区'] },
    { region: '原阳县', parks: ['原阳县先进制造业开发区'] },
    { region: '封丘县', parks: ['封丘县先进制造业开发区'] },
    { region: '卫辉市', parks: ['卫辉市先进制造业开发区'] },
    { region: '辉县市', parks: ['辉县经济技术开发区'] },
    { region: '长垣市', parks: ['长垣经济技术开发区'] }
  ];
  
  const stmt = db.prepare("INSERT INTO region_park_relations (region_id, park_id, is_primary) VALUES (?, ?, ?)");
  
  let processed = 0;
  const totalRelations = relations.reduce((sum, rel) => sum + rel.parks.length, 0);
  
  relations.forEach(relation => {
    db.get("SELECT id FROM regions WHERE name = ?", [relation.region], (err, region) => {
      if (err || !region) {
        processed += relation.parks.length;
        return;
      }
      
      relation.parks.forEach((parkName, index) => {
        db.get("SELECT id FROM parks WHERE name = ?", [parkName], (err, park) => {
          if (!err && park) {
            const isPrimary = index === 0; // 第一个园区作为主要归属
            stmt.run(region.id, park.id, isPrimary);
          }
          
          processed++;
          if (processed === totalRelations) {
            stmt.finalize();
            console.log('区域园区关联关系建立完成');
          }
        });
      });
    });
  });
} 