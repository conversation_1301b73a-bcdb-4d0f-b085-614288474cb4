const { getDatabase } = require('../config/database');

class Park {
  // 获取所有园区
  static getAll(regionId = null) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      if (regionId) {
        // 根据区域获取园区
        const sql = `
          SELECT DISTINCT p.* FROM parks p
          JOIN region_park_relations rpr ON p.id = rpr.park_id
          WHERE rpr.region_id = ?
          ORDER BY p.name
        `;
        db.all(sql, [regionId], (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      } else {
        // 获取所有园区
        db.all("SELECT * FROM parks ORDER BY name", (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      }
    });
  }

  // 分页获取园区
  static getPaginated(page = 1, pageSize = 10, search = '') {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const offset = (page - 1) * pageSize;
      
      let whereClause = '';
      let params = [];
      
      if (search) {
        whereClause = 'WHERE name LIKE ?';
        params.push(`%${search}%`);
      }
      
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM parks ${whereClause}`;
      db.get(countSql, params, (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 获取分页数据
        const dataSql = `SELECT * FROM parks ${whereClause} ORDER BY name LIMIT ? OFFSET ?`;
        const dataParams = [...params, pageSize, offset];
        
        db.all(dataSql, dataParams, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total: countResult.total,
              page,
              pageSize,
              totalPages: Math.ceil(countResult.total / pageSize)
            });
          }
        });
      });
    });
  }

  // 根据ID获取园区
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.get("SELECT * FROM parks WHERE id = ?", [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建园区
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, files } = data;
      const filesJson = files ? JSON.stringify(files) : null;
      db.run(
        "INSERT INTO parks (name, files) VALUES (?, ?)",
        [name, filesJson],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, ...data });
          }
        }
      );
    });
  }

  // 更新园区
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, files } = data;
      const filesJson = files ? JSON.stringify(files) : null;
      db.run(
        "UPDATE parks SET name = ?, files = ? WHERE id = ?",
        [name, filesJson, id],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id, ...data });
          }
        }
      );
    });
  }

  // 删除园区
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM parks WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }

  // 获取园区的要求
  static getRequirements(parkId) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT * FROM park_requirements 
        WHERE park_id = ? AND is_active = 1 
        ORDER BY sort_order, id
      `;
      db.all(sql, [parkId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 添加园区要求
  static addRequirement(parkId, requirement) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { requirement_title, requirement_content, category, sort_order } = requirement;
      db.run(
        `INSERT INTO park_requirements
         (park_id, requirement_title, requirement_content, category, sort_order)
         VALUES (?, ?, ?, ?, ?)`,
        [parkId, requirement_title, requirement_content, category || '其他要求', sort_order || 0],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, park_id: parkId, ...requirement });
          }
        }
      );
    });
  }

  // 更新园区要求
  static updateRequirement(requirementId, requirement) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { requirement_title, requirement_content, category, sort_order } = requirement;
      db.run(
        `UPDATE park_requirements
         SET requirement_title = ?, requirement_content = ?, category = ?,
             sort_order = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [requirement_title, requirement_content, category, sort_order, requirementId],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: requirementId, ...requirement });
          }
        }
      );
    });
  }

  // 删除园区要求
  static deleteRequirement(requirementId) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM park_requirements WHERE id = ?", [requirementId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }
}

module.exports = Park;
