const { initDatabase } = require('./config/database');
const IndustryClassification = require('./models/IndustryClassification');

async function testClassificationFix() {
  try {
    await initDatabase();
    
    console.log('=== 测试行业分类修复 ===');
    
    // 测试具体的环评项目
    const testCases = [
      '农产品基地项目（含药材基地）',
      '经济林基地项目',
      '牲畜饲养',
      '海水养殖',
      '烟煤和无烟煤开采洗选'
    ];
    
    for (const testCase of testCases) {
      console.log(`\n--- 测试: ${testCase} ---`);
      
      try {
        const result = await IndustryClassification.getDetail(testCase);
        console.log('✅ 成功找到分类:');
        console.log(`  主分类: ${result.main.primary_name} (${result.main.code})`);
        console.log(`  层级: ${result.main.level}`);
        console.log(`  描述: ${result.main.detailed_description || '无'}`);
        console.log(`  子分类数量: ${result.descendants.length}`);
        
        if (result.descendants.length > 0) {
          console.log('  前3个子分类:');
          result.descendants.slice(0, 3).forEach(desc => {
            console.log(`    - ${desc.primary_name} (${desc.code})`);
          });
        }
      } catch (error) {
        console.log('❌ 查找失败:', error.message);
      }
    }
    
    // 测试国民经济行业分类名称
    console.log('\n--- 测试国民经济行业分类名称 ---');
    const classificationTests = ['农业', '制造业', '建筑业'];
    
    for (const testCase of classificationTests) {
      console.log(`\n测试: ${testCase}`);
      
      try {
        const result = await IndustryClassification.getDetail(testCase);
        console.log('✅ 成功找到分类:');
        console.log(`  主分类: ${result.main.primary_name} (${result.main.code})`);
        console.log(`  子分类数量: ${result.descendants.length}`);
      } catch (error) {
        console.log('❌ 查找失败:', error.message);
      }
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testClassificationFix();
