const { getDatabase } = require('../config/database');

class IndustryFocusPoint {
  // 分页获取行业关注点
  static getPaginated(page = 1, pageSize = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const offset = (page - 1) * pageSize;
      
      let whereClause = 'WHERE 1=1';
      let params = [];
      
      // 搜索条件
      if (filters.search) {
        whereClause += ' AND (ifp.name LIKE ? OR ifp.description LIKE ? OR ifp.keywords LIKE ?)';
        params.push(`%${filters.search}%`, `%${filters.search}%`, `%${filters.search}%`);
      }

      if (filters.industry_id) {
        whereClause += ' AND r.industry_id = ?';
        params.push(filters.industry_id);
      }
      
      if (filters.is_active !== undefined && filters.is_active !== '') {
        whereClause += ' AND ifp.is_active = ?';
        params.push(filters.is_active);
      }
      
      // 获取总数
      const countSql = `
        SELECT COUNT(DISTINCT ifp.id) as total
        FROM industry_focus_points ifp
        LEFT JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
        LEFT JOIN industries i ON r.industry_id = i.id
        ${whereClause}
      `;
      
      db.get(countSql, params, (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 获取分页数据
        const dataSql = `
          SELECT DISTINCT ifp.*,
                 GROUP_CONCAT(i.name) as industry_names,
                 GROUP_CONCAT(i.id) as industry_ids
          FROM industry_focus_points ifp
          LEFT JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
          LEFT JOIN industries i ON r.industry_id = i.id
          ${whereClause}
          GROUP BY ifp.id
          ORDER BY ifp.sort_order, ifp.id
          LIMIT ? OFFSET ?
        `;
        const dataParams = [...params, pageSize, offset];
        
        db.all(dataSql, dataParams, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total: countResult.total,
              page,
              pageSize,
              totalPages: Math.ceil(countResult.total / pageSize)
            });
          }
        });
      });
    });
  }

  // 根据ID获取行业关注点
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT ifp.*,
               GROUP_CONCAT(i.name) as industry_names,
               GROUP_CONCAT(i.id) as industry_ids
        FROM industry_focus_points ifp
        LEFT JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
        LEFT JOIN industries i ON r.industry_id = i.id
        WHERE ifp.id = ?
        GROUP BY ifp.id
      `;
      db.get(sql, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          if (row) {
            // 处理关联的行业ID数组
            row.industry_ids = row.industry_ids ? row.industry_ids.split(',').map(id => parseInt(id)) : [];
            row.industry_names = row.industry_names ? row.industry_names.split(',') : [];
          }
          resolve(row);
        }
      });
    });
  }

  // 创建行业关注点
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, keywords, policy_files, industry_ids, sort_order, is_active } = data;

      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        // 创建关注点
        const policyFilesJson = policy_files ? JSON.stringify(policy_files) : null;

        db.run(
          `INSERT INTO industry_focus_points (name, description, keywords, policy_files, sort_order, is_active)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [name, description, keywords, policyFilesJson, sort_order || 0, is_active !== false],
          function(err) {
            if (err) {
              db.run("ROLLBACK");
              reject(err);
              return;
            }

            const pointId = this.lastID;

            // 创建行业关联
            if (industry_ids && industry_ids.length > 0) {
              let completed = 0;
              let hasError = false;

              industry_ids.forEach(industryId => {
                db.run(
                  'INSERT INTO industry_focus_point_relations (industry_focus_point_id, industry_id) VALUES (?, ?)',
                  [pointId, industryId],
                  (err) => {
                    if (err && !hasError) {
                      hasError = true;
                      db.run("ROLLBACK");
                      reject(err);
                      return;
                    }

                    completed++;
                    if (completed === industry_ids.length && !hasError) {
                      db.run("COMMIT", (err) => {
                        if (err) {
                          reject(err);
                        } else {
                          resolve({ id: pointId, ...data });
                        }
                      });
                    }
                  }
                );
              });
            } else {
              db.run("COMMIT", (err) => {
                if (err) {
                  reject(err);
                } else {
                  resolve({ id: pointId, ...data });
                }
              });
            }
          }
        );
      });
    });
  }

  // 更新行业关注点
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, keywords, policy_files, industry_ids, sort_order, is_active } = data;

      db.serialize(() => {
        db.run("BEGIN TRANSACTION");

        // 更新关注点基本信息
        const policyFilesJson = policy_files ? JSON.stringify(policy_files) : null;

        db.run(
          `UPDATE industry_focus_points SET
            name = ?, description = ?, keywords = ?, policy_files = ?,
            sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [name, description, keywords, policyFilesJson, sort_order, is_active, id],
          function(err) {
            if (err) {
              db.run("ROLLBACK");
              reject(err);
              return;
            }

            // 删除现有关联
            db.run(
              'DELETE FROM industry_focus_point_relations WHERE industry_focus_point_id = ?',
              [id],
              (err) => {
                if (err) {
                  db.run("ROLLBACK");
                  reject(err);
                  return;
                }

                // 创建新的行业关联
                if (industry_ids && industry_ids.length > 0) {
                  let completed = 0;
                  let hasError = false;

                  industry_ids.forEach(industryId => {
                    db.run(
                      'INSERT INTO industry_focus_point_relations (industry_focus_point_id, industry_id) VALUES (?, ?)',
                      [id, industryId],
                      (err) => {
                        if (err && !hasError) {
                          hasError = true;
                          db.run("ROLLBACK");
                          reject(err);
                          return;
                        }

                        completed++;
                        if (completed === industry_ids.length && !hasError) {
                          db.run("COMMIT", (err) => {
                            if (err) {
                              reject(err);
                            } else {
                              resolve({ id, ...data });
                            }
                          });
                        }
                      }
                    );
                  });
                } else {
                  db.run("COMMIT", (err) => {
                    if (err) {
                      reject(err);
                    } else {
                      resolve({ id, ...data });
                    }
                  });
                }
              }
            );
          }
        );
      });
    });
  }

  // 删除行业关注点
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM industry_focus_points WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }

  // 根据行业ID获取启用的行业关注点
  static getByIndustryId(industryId) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT ifp.*,
               GROUP_CONCAT(i.name) as industry_names,
               GROUP_CONCAT(i.id) as industry_ids
        FROM industry_focus_points ifp
        LEFT JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
        LEFT JOIN industries i ON r.industry_id = i.id
        WHERE r.industry_id = ? AND ifp.is_active = 1
        GROUP BY ifp.id
        ORDER BY ifp.sort_order, ifp.id
      `;
      db.all(sql, [industryId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const processedRows = rows.map(row => ({
            ...row,
            industry_ids: row.industry_ids ? row.industry_ids.split(',').map(id => parseInt(id)) : [],
            industry_names: row.industry_names || ''
          }));
          resolve(processedRows);
        }
      });
    });
  }

  // 获取所有启用的行业关注点
  static getActive() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT ifp.*,
               GROUP_CONCAT(i.name) as industry_names,
               GROUP_CONCAT(i.id) as industry_ids
        FROM industry_focus_points ifp
        LEFT JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
        LEFT JOIN industries i ON r.industry_id = i.id
        WHERE ifp.is_active = 1
        GROUP BY ifp.id
        ORDER BY ifp.sort_order, ifp.id
      `;
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const processedRows = rows.map(row => ({
            ...row,
            industry_ids: row.industry_ids ? row.industry_ids.split(',').map(id => parseInt(id)) : [],
            industry_names: row.industry_names || ''
          }));
          resolve(processedRows);
        }
      });
    });
  }

  // 根据ID数组获取行业关注点
  static getByIds(ids) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      if (!ids || ids.length === 0) {
        resolve([]);
        return;
      }

      // 构建IN查询条件
      const placeholders = ids.map(() => '?').join(',');
      const sql = `
        SELECT ifp.*,
               GROUP_CONCAT(i.name) as industry_names,
               GROUP_CONCAT(i.id) as industry_ids
        FROM industry_focus_points ifp
        LEFT JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
        LEFT JOIN industries i ON r.industry_id = i.id
        WHERE ifp.is_active = 1 AND ifp.id IN (${placeholders})
        GROUP BY ifp.id
        ORDER BY ifp.sort_order, ifp.id
      `;

      db.all(sql, ids, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          // 处理每行数据的关联行业信息
          const processedRows = rows.map(row => ({
            ...row,
            industry_ids: row.industry_ids ? row.industry_ids.split(',').map(id => parseInt(id)) : [],
            industry_names: row.industry_names || ''
          }));
          resolve(processedRows);
        }
      });
    });
  }

  // 根据关键词搜索行业关注点
  static searchByKeywords(keywords) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      if (!keywords || keywords.length === 0) {
        resolve([]);
        return;
      }

      // 构建搜索条件
      const keywordConditions = keywords.map(() => 'ifp.keywords LIKE ?').join(' OR ');
      const sql = `
        SELECT ifp.*,
               GROUP_CONCAT(i.name) as industry_names,
               GROUP_CONCAT(i.id) as industry_ids
        FROM industry_focus_points ifp
        LEFT JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
        LEFT JOIN industries i ON r.industry_id = i.id
        WHERE ifp.is_active = 1 AND (${keywordConditions})
        GROUP BY ifp.id
        ORDER BY ifp.sort_order, ifp.id
      `;

      const params = keywords.map(keyword => `%${keyword}%`);

      db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const processedRows = rows.map(row => ({
            ...row,
            industry_ids: row.industry_ids ? row.industry_ids.split(',').map(id => parseInt(id)) : [],
            industry_names: row.industry_names || ''
          }));
          resolve(processedRows);
        }
      });
    });
  }

  // 更新排序
  static updateSortOrder(updates) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");
        
        let completed = 0;
        let hasError = false;
        
        updates.forEach(({ id, sort_order }) => {
          db.run(
            "UPDATE industry_focus_points SET sort_order = ? WHERE id = ?",
            [sort_order, id],
            function(err) {
              if (err && !hasError) {
                hasError = true;
                db.run("ROLLBACK");
                reject(err);
                return;
              }
              
              completed++;
              if (completed === updates.length && !hasError) {
                db.run("COMMIT", (err) => {
                  if (err) {
                    reject(err);
                  } else {
                    resolve({ updated: completed });
                  }
                });
              }
            }
          );
        });
      });
    });
  }

  // 根据分类信息获取匹配的行业关注点（支持层级匹配）
  static getByClassificationInfo(classificationInfo) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      if (!classificationInfo) {
        resolve([]);
        return;
      }

      // 获取当前分类及其所有父级分类的ID
      // 这样当用户选择小类时，也能匹配到设置在大类上的关注点
      this.getAllParentClassificationIds(classificationInfo.id)
        .then(allIds => {
          if (allIds.length === 0) {
            resolve([]);
            return;
          }

          // 查询关联到这些分类的行业关注点
          const placeholders = allIds.map(() => '?').join(',');
          const sql = `
            SELECT DISTINCT ifp.*,
                   GROUP_CONCAT(i.name) as industry_names,
                   GROUP_CONCAT(i.id) as industry_ids
            FROM industry_focus_points ifp
            INNER JOIN industry_focus_point_relations r ON ifp.id = r.industry_focus_point_id
            INNER JOIN industries i ON r.industry_id = i.id
            WHERE ifp.is_active = 1 AND r.industry_id IN (${placeholders})
            GROUP BY ifp.id
            ORDER BY ifp.sort_order, ifp.id
          `;

          db.all(sql, allIds, (err, rows) => {
            if (err) {
              reject(err);
            } else {
              const processedRows = rows.map(row => ({
                ...row,
                industry_ids: row.industry_ids ? row.industry_ids.split(',').map(id => parseInt(id)) : [],
                industry_names: row.industry_names || ''
              }));
              resolve(processedRows);
            }
          });
        })
        .catch(reject);
    });
  }

  // 获取行业的相关行业ID（用于查找相关的行业关注点）
  // 对于分类管理名录，我们可以通过行业分类来查找相关行业
  static getAllParentClassificationIds(industryId) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();

      // 获取当前行业的分类信息
      const sql = `
        SELECT category FROM industries WHERE id = ?
      `;

      db.get(sql, [industryId], (err, row) => {
        if (err) {
          reject(err);
        } else if (!row) {
          resolve([industryId]); // 如果找不到，至少返回自己
        } else {
          // 查找同一分类下的所有行业
          const categorySql = `
            SELECT id FROM industries WHERE category = ?
          `;

          db.all(categorySql, [row.category], (err, rows) => {
            if (err) {
              reject(err);
            } else {
              const ids = rows.map(r => r.id);
              resolve(ids);
            }
          });
        }
      });
    });
  }
}

module.exports = IndustryFocusPoint;
