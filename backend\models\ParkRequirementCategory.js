const { getDatabase } = require('../config/database');

class ParkRequirementCategory {
  // 获取所有分类
  static getAll() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = 'SELECT * FROM park_requirement_categories ORDER BY sort_order, id';
      
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据ID获取分类
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = 'SELECT * FROM park_requirement_categories WHERE id = ?';
      
      db.get(sql, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建分类
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, sort_order = 0 } = data;
      
      const sql = `
        INSERT INTO park_requirement_categories (name, description, sort_order, created_at, updated_at)
        VALUES (?, ?, ?, datetime('now'), datetime('now'))
      `;
      
      db.run(sql, [name, description, sort_order], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...data });
        }
      });
    });
  }

  // 更新分类
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const { name, description, sort_order } = data;
      
      const sql = `
        UPDATE park_requirement_categories 
        SET name = ?, description = ?, sort_order = ?, updated_at = datetime('now')
        WHERE id = ?
      `;
      
      db.run(sql, [name, description, sort_order, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, ...data });
        }
      });
    });
  }

  // 删除分类
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      
      // 先检查是否有园区要求使用此分类
      const checkSql = 'SELECT COUNT(*) as count FROM park_requirements WHERE category = (SELECT name FROM park_requirement_categories WHERE id = ?)';
      
      db.get(checkSql, [id], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        
        if (row.count > 0) {
          reject(new Error('该分类正在被使用，无法删除'));
          return;
        }
        
        // 删除分类
        const deleteSql = 'DELETE FROM park_requirement_categories WHERE id = ?';
        db.run(deleteSql, [id], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ deleted: this.changes > 0 });
          }
        });
      });
    });
  }

  // 检查分类名称是否已存在
  static checkNameExists(name, excludeId = null) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      let sql = 'SELECT COUNT(*) as count FROM park_requirement_categories WHERE name = ?';
      let params = [name];
      
      if (excludeId) {
        sql += ' AND id != ?';
        params.push(excludeId);
      }
      
      db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row.count > 0);
        }
      });
    });
  }
}

module.exports = ParkRequirementCategory;
