<template>
  <div class="industry-classification-detail" v-if="classificationData">
    <div class="classification-header">
      <h3>国民经济行业分类</h3>
      <div class="main-classification" v-if="classificationData.main">
        <div class="classification-code">{{ classificationData.main.code }}</div>
        <div class="classification-info">
          <span class="level-badge" :class="`level-${classificationData.main.level}`">
            {{ classificationData.main.level_name }}
          </span>
          <span class="classification-name">{{ classificationData.main.primary_name }}</span>
        </div>
      </div>
    </div>

    <!-- 主分类详细描述 -->
    <div class="classification-description" v-if="classificationData.main?.detailed_description">
      <h4>详细说明</h4>
      <div class="description-content" v-html="formatDescription(classificationData.main.detailed_description)"></div>
    </div>

    <!-- 新的、递归的子分类列表 -->
    <div class="sub-classifications" v-if="nestedTree.length > 0">
      <h4>子分类</h4>
      <div class="sub-classification-list">
        <SubClassificationNode 
          v-for="node in nestedTree"
          :key="node.code"
          :node="node"
        />
      </div>
    </div>

    <!-- 相关分类（如果有重复代码） -->
    <div class="alternative-classifications" v-if="classificationData.alternatives && classificationData.alternatives.length > 0">
      <h4>相关分类</h4>
      <div class="alert alert-info">
        <i class="icon-info"></i>
        注意：以下分类包含相同数字代码，请根据实际业务选择正确的分类
      </div>
      <div class="alternative-list">
        <div 
          class="alternative-item" 
          v-for="alt in classificationData.alternatives" 
          :key="alt.code"
        >
          <div class="alternative-header">
            <span class="alt-code">{{ alt.code }}</span>
            <span class="level-badge" :class="`level-${alt.level}`">{{ alt.level_name }}</span>
            <span class="alt-name">{{ alt.primary_name }}</span>
          </div>
          <div class="alt-path" v-if="alt.full_path_description">
            <small>{{ alt.full_path_description }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import SubClassificationNode from './SubClassificationNode.vue';

export default {
  name: 'IndustryClassificationDetail',
  components: {
    SubClassificationNode
  },
  props: {
    industryInfo: {
      type: Object,
      required: true,
      default: () => ({ code: '', name: '' })
    }
  },
  data() {
    return {
      classificationData: null,
      loading: false,
      error: null
    }
  },
  computed: {
    nestedTree() {
      if (!this.classificationData || !this.classificationData.descendants || this.classificationData.descendants.length === 0) {
        return [];
      }
      
      const nodes = this.classificationData.descendants;
      const rootParentCode = this.classificationData.main.code;
      const nodeMap = {};
      
      nodes.forEach(node => {
        nodeMap[node.code] = { ...node, children: [] };
      });

      const tree = [];
      nodes.forEach(node => {
        if (node.parent_code === rootParentCode) {
          tree.push(nodeMap[node.code]);
        } else if (nodeMap[node.parent_code]) {
          nodeMap[node.parent_code].children.push(nodeMap[node.code]);
        }
      });
      
      return tree;
    }
  },
  watch: {
    industryInfo: {
      handler(newInfo) {
        if (newInfo && newInfo.name) { // 始终用name来触发查询
          this.fetchClassificationDetail(newInfo.name);
        } else {
          this.classificationData = null;
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchClassificationDetail(name) {
      this.loading = true;
      this.error = null;
      try {
        const url = `/api/industry-classification/detail?name=${encodeURIComponent(name)}`;
        const response = await fetch(url);
        const result = await response.json();
        
        if (result.code === 200) {
          this.classificationData = result.data;
        } else {
          this.classificationData = null;
          this.error = result.message || '获取行业分类详情失败';
        }
      } catch (error) {
        this.classificationData = null;
        console.error('获取行业分类详情失败:', error);
        this.error = '网络错误，请稍后重试';
      } finally {
        this.loading = false;
      }
    },
    formatDescription(description) {
      if (!description) return '';
      return description
        .replace(/◇ 包括[^：]*：/g, '<div class="include-section"><strong>$&</strong></div>')
        .replace(/◆ 不包括：/g, '<div class="exclude-section"><strong>$&</strong></div>')
        .replace(/— ([^；\n]+)[；\n]/g, '<div class="list-item">• $1</div>')
        .replace(/\n/g, '<br/>');
    }
  }
}
</script>

<style scoped>
.industry-classification-detail {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin: 16px 0;
}

.classification-header h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.main-classification {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #3498db;
}

.classification-code {
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  background: #ecf0f1;
  padding: 4px 8px;
  border-radius: 4px;
}

.classification-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.level-1 { background-color: #e74c3c; }
.level-2 { background-color: #f39c12; }
.level-3 { background-color: #3498db; }
.level-4 { background-color: #27ae60; }

.classification-name {
  font-weight: 600;
  color: #2c3e50;
}

.classification-description {
  margin: 16px 0;
}

.classification-description h4,
.sub-classifications h4,
.alternative-classifications h4 {
  margin: 0 0 12px 0;
  color: #34495e;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #bdc3c7;
  padding-bottom: 4px;
}

.description-content {
  background: white;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  line-height: 1.6;
}

.description-content :deep(.include-section) {
  color: #27ae60;
  font-weight: 600;
  margin: 8px 0 4px 0;
}

.description-content :deep(.exclude-section) {
  color: #e74c3c;
  font-weight: 600;
  margin: 8px 0 4px 0;
}

.description-content :deep(.list-item) {
  margin-left: 16px;
  margin: 2px 0 2px 16px;
  color: #5a6c7d;
}

.sub-classification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alternative-classifications {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.alert {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-info {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.alternative-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alternative-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
}

.alternative-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.alt-code {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #e74c3c;
  background: #fdf2f2;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 14px;
}

.alt-name {
  font-weight: 500;
  color: #2c3e50;
}

.alt-path {
  margin-top: 4px;
  color: #6c757d;
}

.icon-info::before {
  content: "ℹ";
  font-weight: bold;
}

@media (max-width: 768px) {
  .main-classification {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .classification-info {
    flex-wrap: wrap;
  }
  
  .sub-classification-header,
  .alternative-header {
    flex-wrap: wrap;
  }
}
</style> 