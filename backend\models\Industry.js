const { getDatabase } = require('../config/database');

class Industry {
  // 分页获取行业分类（分类管理名录）
  static getPaginated(page = 1, pageSize = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const offset = (page - 1) * pageSize;
      
      let whereClause = 'WHERE 1=1';
      let params = [];
      
      // 搜索条件
      if (filters.search) {
        whereClause += ' AND (name LIKE ? OR code LIKE ? OR category LIKE ?)';
        params.push(`%${filters.search}%`, `%${filters.search}%`, `%${filters.search}%`);
      }
      
      if (filters.category) {
        whereClause += ' AND category = ?';
        params.push(filters.category);
      }
      
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM industries ${whereClause}`;
      
      db.get(countSql, params, (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 获取分页数据
        const dataSql = `
          SELECT * FROM industries 
          ${whereClause}
          ORDER BY category_order, item_number, code
          LIMIT ? OFFSET ?
        `;
        const dataParams = [...params, pageSize, offset];
        
        db.all(dataSql, dataParams, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              data: rows,
              total: countResult.total,
              page,
              pageSize,
              totalPages: Math.ceil(countResult.total / pageSize)
            });
          }
        });
      });
    });
  }

  // 获取所有分类
  static getCategories() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT DISTINCT category 
        FROM industries 
        WHERE category IS NOT NULL 
        ORDER BY category_order
      `;
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows.map(row => row.category));
        }
      });
    });
  }

  // 根据ID获取行业
  static getById(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.get("SELECT * FROM industries WHERE id = ?", [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 创建行业
  static create(data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const {
        name, code, category, category_order, item_number,
        report_book, report_table, registration_form, sensitive_area_meaning
      } = data;
      
      db.run(
        `INSERT INTO industries (
          name, code, category, category_order, item_number,
          report_book, report_table, registration_form, sensitive_area_meaning
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          name, code, category, category_order, item_number,
          report_book, report_table, registration_form, sensitive_area_meaning
        ],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, ...data });
          }
        }
      );
    });
  }

  // 更新行业
  static update(id, data) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const {
        name, code, category, category_order, item_number,
        report_book, report_table, registration_form, sensitive_area_meaning
      } = data;
      
      db.run(
        `UPDATE industries SET 
          name = ?, code = ?, category = ?, category_order = ?, item_number = ?,
          report_book = ?, report_table = ?, registration_form = ?, sensitive_area_meaning = ?
         WHERE id = ?`,
        [
          name, code, category, category_order, item_number,
          report_book, report_table, registration_form, sensitive_area_meaning, id
        ],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id, ...data });
          }
        }
      );
    });
  }

  // 删除行业
  static delete(id) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      db.run("DELETE FROM industries WHERE id = ?", [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ deleted: this.changes > 0 });
        }
      });
    });
  }

  // 获取行业环评要求
  static getRequirements(industryId) {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const sql = `
        SELECT
          id, code, name, category,
          report_book, report_table, registration_form, sensitive_area_meaning
        FROM industries
        WHERE id = ?
      `;

      db.get(sql, [industryId], (err, industry) => {
        if (err) {
          reject(err);
        } else if (!industry) {
          reject(new Error('未找到该行业'));
        } else {
          // 将单行结果适配前端期望的格式
          const result = {
            industry: {
              id: industry.id,
              code: industry.code,
              name: industry.name,
              category: industry.category,
            },
            tableHeader: {
              title: '建设项目环境影响评价分类管理名录（2021年版）',
              columns: [
                '项目类别',
                '报告书',
                '报告表',
                '登记表',
                '本表中环境敏感区含义'
              ]
            },
            requirements: [{
              projectTypes: industry.name,
              reportBook: industry.report_book,
              reportTable: industry.report_table,
              registrationForm: industry.registration_form,
              sensitiveAreaMeaning: industry.sensitive_area_meaning
            }]
          };

          resolve(result);
        }
      });
    });
  }

  // 获取级联选择器格式的行业数据
  static getCascadeData() {
    return new Promise((resolve, reject) => {
      const db = getDatabase();
      const query = `
        SELECT id, code, name, category, category_order, item_number
        FROM industries 
        ORDER BY category_order, item_number, code
      `;
      
      db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }
        
        // 按大类分组，转换为级联选择器需要的格式
        const grouped = {};
        rows.forEach(row => {
          if (!grouped[row.category]) {
            grouped[row.category] = {
              id: `category_${row.category}`,
              name: row.category,
              children: []
            };
          }
          
          // 如果有具体的行业代码，添加为子项
          if (row.code) {
            grouped[row.category].children.push({
              id: row.id,
              name: `${row.name}${row.code}`,
              code: row.code,
              category: row.category
            });
          } else {
            // 如果没有代码，直接作为大类下的项目
            grouped[row.category].children.push({
              id: row.id,
              name: row.name,
              category: row.category
            });
          }
        });
        
        // 转换为数组并排序
        const result = Object.values(grouped).sort((a, b) => {
          const aOrder = rows.find(r => r.category === a.name.replace('category_', ''))?.category_order || 0;
          const bOrder = rows.find(r => r.category === b.name.replace('category_', ''))?.category_order || 0;
          return aOrder - bOrder;
        });
        
        resolve(result);
      });
    });
  }
}

module.exports = Industry;
