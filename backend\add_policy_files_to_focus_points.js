const { initDatabase, getDatabase } = require('./config/database');

async function addPolicyFilesToFocusPoints() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 为关注点添加政策文件字段 ===');
    
    // 1. 为通用关注点表添加政策文件字段
    console.log('为通用关注点表添加政策文件字段...');
    await new Promise((resolve, reject) => {
      db.run(`
        ALTER TABLE general_focus_points 
        ADD COLUMN policy_files TEXT DEFAULT NULL
      `, (err) => {
        if (err && !err.message.includes('duplicate column name')) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
    
    // 2. 为行业关注点表添加政策文件字段
    console.log('为行业关注点表添加政策文件字段...');
    await new Promise((resolve, reject) => {
      db.run(`
        ALTER TABLE industry_focus_points 
        ADD COLUMN policy_files TEXT DEFAULT NULL
      `, (err) => {
        if (err && !err.message.includes('duplicate column name')) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
    
    // 3. 添加一些测试数据
    console.log('添加测试政策文件数据...');
    
    // 为通用关注点添加政策文件
    const generalPolicyFiles = [
      {
        id: 1,
        files: JSON.stringify([
          { name: '建设项目环境影响评价分类管理名录', url: 'https://example.com/policy1.pdf' },
          { name: '环境影响评价公众参与办法', url: 'https://example.com/policy2.pdf' }
        ])
      },
      {
        id: 2,
        files: JSON.stringify([
          { name: '固定资产投资项目节能审查办法', url: 'https://example.com/policy3.pdf' },
          { name: '重点用能单位节能管理办法', url: 'https://example.com/policy4.pdf' }
        ])
      }
    ];
    
    for (const item of generalPolicyFiles) {
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE general_focus_points SET policy_files = ? WHERE id = ?',
          [item.files, item.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }
    
    // 为行业关注点添加政策文件
    const industryPolicyFiles = [
      {
        id: 1,
        files: JSON.stringify([
          { name: '挥发性有机物无组织排放控制标准', url: 'https://example.com/industry1.pdf' },
          { name: 'VOCs治理技术指南', url: 'https://example.com/industry2.pdf' }
        ])
      },
      {
        id: 2,
        files: JSON.stringify([
          { name: '重金属污染综合防治十四五规划', url: 'https://example.com/industry3.pdf' },
          { name: '土壤污染防治法实施细则', url: 'https://example.com/industry4.pdf' }
        ])
      }
    ];
    
    for (const item of industryPolicyFiles) {
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE industry_focus_points SET policy_files = ? WHERE id = ?',
          [item.files, item.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }
    
    // 4. 验证结果
    const generalCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM general_focus_points WHERE policy_files IS NOT NULL', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    const industryCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM industry_focus_points WHERE policy_files IS NOT NULL', (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });
    
    console.log('\n=== 添加完成 ===');
    console.log('通用关注点有政策文件的数量:', generalCount);
    console.log('行业关注点有政策文件的数量:', industryCount);
    
    // 显示示例数据
    const sampleGeneral = await new Promise((resolve, reject) => {
      db.get('SELECT name, policy_files FROM general_focus_points WHERE policy_files IS NOT NULL LIMIT 1', (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    if (sampleGeneral) {
      console.log('\n=== 示例数据 ===');
      console.log('通用关注点:', sampleGeneral.name);
      console.log('政策文件:', JSON.parse(sampleGeneral.policy_files));
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('添加失败:', error);
    process.exit(1);
  }
}

addPolicyFilesToFocusPoints();
