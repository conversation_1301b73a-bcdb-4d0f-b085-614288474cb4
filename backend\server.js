const express = require('express');
const cors = require('cors');

const { initDatabase } = require('./config/database');
const apiRoutes = require('./routes/api');
const adminRoutes = require('./routes/admin');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 路由
app.use('/api', apiRoutes);
app.use('/api/admin', adminRoutes);

// 启动服务器
initDatabase().then(() => {
  app.listen(PORT, () => {
    console.log(`后端服务器运行在 http://localhost:${PORT}`);
    console.log('前端请通过 http://localhost:3000 访问（Vite代理）');
  });
}).catch(err => {
  console.error('数据库初始化失败:', err);
  process.exit(1);
});
