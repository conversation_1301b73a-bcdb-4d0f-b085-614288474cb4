<template>
  <section v-if="hasResults" class="results-section">
    <el-tabs type="border-card" class="main-tabs">
      <!-- 必须关注 -->
      <el-tab-pane v-if="hasRequiredResults">
        <template #label>
          <span class="tab-label">
            <el-icon><WarningFilled /></el-icon> 必须关注
          </span>
        </template>
        
        <el-tabs v-model="activeRequiredTab" type="card" class="content-tabs">
          <!-- 分类管理名录 -->
          <el-tab-pane label="分类管理名录" name="requirements" v-if="industryRequirements">
            <IndustryRequirementsTable :industryRequirements="industryRequirements" />
          </el-tab-pane>
          
          <!-- 国民经济行业 -->
          <el-tab-pane label="国民经济行业" name="classification" v-if="industryInfo.name">
            <IndustryClassificationDetail :industry-info="industryInfo" />
          </el-tab-pane>

          <!-- 三线一单 -->
          <el-tab-pane label="三线一单" name="sanxianyidan" v-if="requiredResults.sanxianyidan && requiredResults.sanxianyidan.length > 0">
            <SanxianyidanResult :units="requiredResults.sanxianyidan" />
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>

      <!-- 可能需要关注 -->
      <el-tab-pane v-if="optionalResults.length > 0">
        <template #label>
          <span class="tab-label">
            <el-icon><InfoFilled /></el-icon> 可能需要关注
          </span>
        </template>
        <div class="optional-results-container">
          <PolicyResultItem 
            v-for="policy in optionalResults" 
            :key="policy.id" 
            :policy="policy" 
          />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 无结果提示 -->
    <div v-if="!hasRequiredResults && optionalResults.length === 0" class="section-card empty-card">
      <div class="empty-content">
        <i class="icon-empty"></i>
        <h3>未找到相关政策信息</h3>
        <p>请调整筛选条件后重新查询</p>
      </div>
    </div>
  </section>
</template>

<script>
import { ref, computed, watch } from 'vue'
import SanxianyidanResult from './SanxianyidanResult.vue'
import IndustryRequirementsTable from './IndustryRequirementsTable.vue'
import IndustryClassificationDetail from './IndustryClassificationDetail.vue'
import PolicyResultItem from './PolicyResultItem.vue'
import { ElTabs, ElTabPane, ElIcon } from 'element-plus'
import { WarningFilled, InfoFilled } from '@element-plus/icons-vue'

export default {
  name: 'SearchResults',
  components: {
    SanxianyidanResult,
    IndustryRequirementsTable,
    IndustryClassificationDetail,
    PolicyResultItem,
    ElTabs,
    ElTabPane,
    ElIcon,
    WarningFilled,
    InfoFilled,
  },
  props: {
    requiredResults: {
      type: Object,
      default: () => ({ sanxianyidan: [], industry_policies: [], park_policies: [] })
    },
    optionalResults: {
      type: Array,
      default: () => []
    },
    industryRequirements: {
      type: Object,
      default: null
    },
    industryInfo: {
      type: Object,
      default: () => ({ code: '', name: '' })
    },
    hasResults: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const activeRequiredTab = ref('requirements');

    const hasRequiredResults = computed(() => {
      return (props.requiredResults.sanxianyidan && props.requiredResults.sanxianyidan.length > 0) || 
             props.industryRequirements ||
             (props.industryInfo && props.industryInfo.name)
    });

    // 智能切换默认显示的子Tab
    watch(() => [props.industryRequirements, props.industryInfo], () => {
        if (props.industryRequirements) {
            activeRequiredTab.value = 'requirements';
        } else if (props.industryInfo.name) {
            activeRequiredTab.value = 'classification';
        } else if (props.requiredResults.sanxianyidan && props.requiredResults.sanxianyidan.length > 0) {
            activeRequiredTab.value = 'sanxianyidan';
        }
    }, { immediate: true });

    return {
      hasRequiredResults,
      activeRequiredTab
    }
  }
}
</script>

<style scoped>
/* Base Styles */
.results-section {
  margin-top: 20px;
}
.main-tabs {
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color, #e5e9f2);
  overflow: hidden;
  background-color: var(--card-bg-color, rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
}
.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
}
.tab-label .el-icon {
  font-size: 1.2em;
}

:deep(.el-tabs__header) {
    background-color: rgba(255,255,255,0.5);
    border-bottom: 1px solid var(--border-color, #e5e9f2);
}

.content-tabs {
  border: none;
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
    border: none;
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
    border-radius: 8px 8px 0 0;
    border: 1px solid transparent;
    border-bottom: none;
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    border-color: var(--border-color, #e5e9f2);
    background-color: #fff;
}


.optional-results-container {
  padding: 15px;
}

/* Empty State */
.empty-card {
  text-align: center;
  padding: 60px 30px;
  background: var(--card-bg-color, rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color, #e5e9f2);
}

.empty-content i {
  font-size: 4rem;
  margin-bottom: 20px;
  display: block;
}

.empty-content h3 {
  color: #374151;
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.empty-content p {
  color: #6b7280;
  margin: 0;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    :deep(.el-tabs__item) {
        padding: 0 12px;
    }
    .tab-label {
        font-size: 0.9rem;
    }
    .empty-card {
        padding: 40px 20px;
    }
}
</style> 