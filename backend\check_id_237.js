const { initDatabase, getDatabase } = require('./config/database');

async function checkData() {
  await initDatabase();
  const db = getDatabase();

  // 查询ID 237的行业数据
  db.get('SELECT * FROM industries WHERE id = 237', (err, row) => {
  if (err) {
    console.error('查询错误:', err);
  } else if (row) {
    console.log('ID 237 对应的行业数据:');
    console.log('ID:', row.id);
    console.log('名称:', row.name);
    console.log('代码:', row.code);
    console.log('分类:', row.category);
    console.log('完整数据:', JSON.stringify(row, null, 2));
  } else {
    console.log('未找到ID 237的行业数据');
  }
  
  // 同时查询industry_classification表中是否有相关数据
  db.all('SELECT * FROM industry_classification WHERE primary_name LIKE "%卷烟%"', (err, rows) => {
    if (err) {
      console.error('查询分类错误:', err);
    } else {
      console.log('\n行业分类表中的卷烟相关数据:');
      rows.forEach(row => {
        console.log('ID:', row.id, '代码:', row.code, '名称:', row.primary_name, '层级:', row.level);
      });
    }
    
    // 查询所有ID在230-240之间的行业
    db.all('SELECT * FROM industries WHERE id BETWEEN 230 AND 240', (err, rows) => {
      if (err) {
        console.error('查询范围错误:', err);
      } else {
        console.log('\nID 230-240 范围内的行业:');
        rows.forEach(row => {
          console.log('ID:', row.id, '名称:', row.name, '代码:', row.code, '分类:', row.category);
        });
      }
      process.exit(0);
    });
  });
  });
}

checkData().catch(console.error);
