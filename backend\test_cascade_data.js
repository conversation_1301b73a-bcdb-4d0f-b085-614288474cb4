const { initDatabase } = require('./config/database');
const IndustryClassification = require('./models/IndustryClassification');

async function testCascadeData() {
  try {
    await initDatabase();
    
    console.log('=== 测试层级数据API ===');
    
    const result = await IndustryClassification.getCascadeData();
    
    console.log('顶级分类数量:', result.length);
    
    // 显示前3个顶级分类及其子分类
    result.slice(0, 3).forEach((topLevel, index) => {
      console.log(`\n${index + 1}. ${topLevel.name} (${topLevel.code})`);
      
      if (topLevel.children) {
        console.log(`   子分类数量: ${topLevel.children.length}`);
        
        // 显示前3个子分类
        topLevel.children.slice(0, 3).forEach((child, childIndex) => {
          console.log(`   ${childIndex + 1}. ${child.name} (${child.code})`);
          
          if (child.children) {
            console.log(`      孙分类数量: ${child.children.length}`);
            // 显示前2个孙分类
            child.children.slice(0, 2).forEach((grandChild, grandIndex) => {
              console.log(`      ${grandIndex + 1}. ${grandChild.name} (${grandChild.code})`);
            });
          }
        });
      }
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testCascadeData();
