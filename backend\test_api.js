const { initDatabase, getDatabase } = require('./config/database');

async function testApi() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 测试园区要求API ===');
    
    // 直接查询园区要求数据
    const requirements = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM park_requirements WHERE park_id = 2', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('园区要求数据:');
    console.log(JSON.stringify(requirements, null, 2));
    
    process.exit(0);
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testApi();
