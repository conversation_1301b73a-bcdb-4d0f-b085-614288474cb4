const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, '..', 'data', 'ltwiki.db');

let db;

// 初始化数据库连接
function initDatabase() {
  return new Promise((resolve, reject) => {
    db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('数据库连接失败:', err);
        reject(err);
      } else {
        console.log('数据库连接成功');
        createTables().then(resolve).catch(reject);
      }
    });
  });
}

// 创建数据表
function createTables() {
  return new Promise((resolve, reject) => {
    const tables = [
      // 区域表
      `CREATE TABLE IF NOT EXISTS regions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(20),
        region_type VARCHAR(20) DEFAULT 'normal',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 园区表
      `CREATE TABLE IF NOT EXISTS parks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(200) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 区域园区关联表
      `CREATE TABLE IF NOT EXISTS region_park_relations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        region_id INTEGER,
        park_id INTEGER,
        is_primary BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (region_id) REFERENCES regions(id),
        FOREIGN KEY (park_id) REFERENCES parks(id)
      )`,
      
      // 行业表
      `CREATE TABLE IF NOT EXISTS industries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        parent_id INTEGER,
        code VARCHAR(20),
        category VARCHAR(100),
        category_order INTEGER,
        item_number INTEGER,
        report_book TEXT,
        report_table TEXT,
        registration_form TEXT,
        sensitive_area_meaning TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES industries(id)
      )`,
      
      // 国民经济行业分类表
      `CREATE TABLE IF NOT EXISTS industry_classification (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code VARCHAR(20) NOT NULL,
        level INTEGER NOT NULL,
        level_name VARCHAR(20),
        primary_name VARCHAR(200) NOT NULL,
        detailed_description TEXT,
        full_path_description TEXT,
        parent_code VARCHAR(20),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 行业关键词表
      `CREATE TABLE IF NOT EXISTS industry_keywords (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        industry_id INTEGER,
        keyword VARCHAR(100),
        FOREIGN KEY (industry_id) REFERENCES industries(id)
      )`,
      
      // 通用标签表
      `CREATE TABLE IF NOT EXISTS general_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag_name VARCHAR(100),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 政策类型表
      `CREATE TABLE IF NOT EXISTS policy_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 政策主表
      `CREATE TABLE IF NOT EXISTS policies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(500) NOT NULL,
        policy_type_id INTEGER NOT NULL,
        publish_org VARCHAR(200),
        publish_date DATE,
        source_file_url TEXT,
        summary TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (policy_type_id) REFERENCES policy_types(id)
      )`,
      
      // 管控单元表（三线一单专用）
      `CREATE TABLE IF NOT EXISTS control_units (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        policy_id INTEGER NOT NULL,
        unit_code VARCHAR(50) NOT NULL,
        unit_name VARCHAR(500) NOT NULL,
        region_id INTEGER,
        park_id INTEGER,
        unit_type VARCHAR(50),
        spatial_constraints TEXT,
        pollution_control TEXT,
        risk_prevention TEXT,
        resource_efficiency TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (policy_id) REFERENCES policies(id),
        FOREIGN KEY (region_id) REFERENCES regions(id),
        FOREIGN KEY (park_id) REFERENCES parks(id)
      )`,
      
      // 通用政策条款表
      `CREATE TABLE IF NOT EXISTS policy_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        policy_id INTEGER NOT NULL,
        region_id INTEGER,
        park_id INTEGER,
        industry_id INTEGER,
        rule_content TEXT NOT NULL,
        rule_type VARCHAR(100),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (policy_id) REFERENCES policies(id),
        FOREIGN KEY (region_id) REFERENCES regions(id),
        FOREIGN KEY (park_id) REFERENCES parks(id),
        FOREIGN KEY (industry_id) REFERENCES industries(id)
      )`,
      
      // 政策标签关联表
      `CREATE TABLE IF NOT EXISTS policy_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        policy_id INTEGER,
        tag_id INTEGER,
        FOREIGN KEY (policy_id) REFERENCES policies(id),
        FOREIGN KEY (tag_id) REFERENCES general_tags(id)
      )`,
      
      // 园区要求分类表
      `CREATE TABLE IF NOT EXISTS park_requirement_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        sort_order INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 开发区要求表
      `CREATE TABLE IF NOT EXISTS park_requirements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        park_id INTEGER NOT NULL,
        requirement_title VARCHAR(200) NOT NULL,
        requirement_content TEXT NOT NULL,
        requirement_type VARCHAR(50),
        category VARCHAR(100),
        sort_order INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (park_id) REFERENCES parks(id)
      )`,
      
      // 关注点表
      `CREATE TABLE IF NOT EXISTS focus_points (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        category VARCHAR(50),
        keywords TEXT,
        point_type VARCHAR(20) DEFAULT 'general',
        industry_id INTEGER,
        sort_order INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (industry_id) REFERENCES industries(id)
      )`
    ];

    let completed = 0;
    tables.forEach((sql, index) => {
      db.run(sql, (err) => {
        if (err) {
          console.error(`创建表失败 ${index}:`, err);
          reject(err);
        } else {
          completed++;
          if (completed === tables.length) {
            console.log('所有数据表创建成功');
            resolve();
          }
        }
      });
    });
  });
}

// 获取数据库实例
function getDatabase() {
  return db;
}

module.exports = {
  initDatabase,
  getDatabase
};
