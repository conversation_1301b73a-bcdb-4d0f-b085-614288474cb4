# 新乡市环保合规查询系统

## 项目简介

新乡市环保合规查询系统是一个基于Vue.js + Node.js + SQLite的环保政策查询平台，为企业和个人提供全面的环保合规指导服务。

## 功能特色

- **行业环评要求查询**：基于《建设项目环境影响评价分类管理名录（2021年版）》的完整行业环评要求查询
- **三线一单查询**：新乡市"三线一单"生态环境准入清单查询
- **基础项目查询**：项目环保要求综合查询
- **智能搜索**：支持行业代码、名称模糊搜索

## 技术架构

### 前端 (frontend/)
- Vue 3 + Composition API
- Element Plus UI组件库
- Vite构建工具
- Axios HTTP客户端

### 后端 (backend/)
- Node.js + Express框架
- SQLite数据库
- CSV数据导入处理
- RESTful API设计

## 项目结构

```
xinxiang-environmental-system/
├── frontend/                 # Vue前端项目
│   ├── src/
│   │   ├── views/           # 页面组件
│   │   ├── utils/           # 工具函数
│   │   ├── App.vue          # 根组件
│   │   ├── main.js          # 入口文件
│   │   └── router.js        # 路由配置
│   ├── package.json         # 前端依赖
│   └── vite.config.js       # Vite配置
├── backend/                 # Node.js后端项目
│   ├── data/               # 数据库文件
│   ├── uploads/            # 上传文件目录
│   ├── server.js           # 服务器主文件
│   ├── package.json        # 后端依赖
│   ├── 三线一单.csv        # 三线一单数据
│   └── 建设项目环境影响评价分类管理名录（2021年版）.csv
├── package.json            # 根项目配置
└── README.md              # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
# 安装所有依赖（根目录、前端、后端）
npm run install:all
```

### 2. 开发模式启动

```bash
# 同时启动前后端开发服务器
npm run dev
```

这将启动：
- 后端服务器：http://localhost:3001
- 前端开发服务器：http://localhost:5173

### 3. 单独启动

```bash
# 只启动后端
npm run dev:backend

# 只启动前端
npm run dev:frontend
```

### 4. 生产部署

```bash
# 构建前端
npm run build

# 启动生产服务器
npm start
```

## 数据说明

### 行业数据
- 来源：《建设项目环境影响评价分类管理名录（2021年版）》
- 总计：293个行业选项，55个大类
- 包含：行业代码、名称、环评要求等完整信息

### 三线一单数据
- 来源：新乡市"三线一单"生态环境准入清单
- 包含：87个管控单元的完整管控要求
- 覆盖：新乡市所有区县和重要园区

## API接口

### 行业相关
- `GET /api/industries` - 获取行业列表
- `GET /api/industries/:code/requirements` - 获取行业环评要求

### 三线一单
- `POST /api/sanxianyidan` - 三线一单查询

### 基础数据
- `GET /api/regions` - 获取区域列表
- `GET /api/parks` - 获取园区列表
- `GET /api/control-units` - 获取管控单元列表

## 开发说明

### 前端开发
- 使用Vue 3 Composition API
- Element Plus组件库
- 响应式设计，支持移动端
- API请求统一管理

### 后端开发
- Express框架，RESTful API设计
- SQLite数据库，轻量级部署
- 自动CSV数据导入
- CORS跨域支持

### 数据库初始化
系统首次启动时会自动：
1. 创建数据库表结构
2. 导入基础数据（区域、园区等）
3. 解析并导入行业数据
4. 解析并导入三线一单数据

## 更新日志

### v1.0.0 (2024-12-18)
- ✅ 完成项目架构重构（前后端分离）
- ✅ 实现行业环评要求查询功能
- ✅ 实现三线一单查询功能
- ✅ 完成293个行业数据导入
- ✅ 完成87个管控单元数据导入
- ✅ Vue前端界面优化
- ✅ API接口标准化

## 许可证

MIT License

## 联系方式

如有问题请联系开发团队。 