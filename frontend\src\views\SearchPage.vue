﻿<template>
  <div class="search-page-container">
    <div class="header-bg"></div>
    <div class="search-page">
      <!-- 页头 -->
      <header class="page-header">
        <div class="logo-title">
            <div class="logo-icon-wrapper">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 7H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    <path d="M3 12H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    <path d="M3 17H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <h1 class="page-title">蓝天Wiki</h1>
        </div>
      </header>

      <!-- 主要内容 -->
      <main class="main-content">
        <div class="container">
          <!-- 搜索表单 -->
          <CollapsibleSearchForm
            :regions="regions"
            :industry-options="industryOptions"
            :general-tags="generalTags"
            :searching="searching"
            v-model:is-collapsed="isFormCollapsed"
            @search="handleSearch"
            @reset="handleReset"
            @optional-change="handleOptionalChange"
            ref="searchFormRef"
          />

          <!-- 查询结果 -->
          <SearchResults
            :required-results="requiredResults"
            :optional-results="optionalResults"
            :industry-requirements="industryRequirements"
            :industry-info="selectedIndustryInfo"
            :has-results="hasSearchResults"
            :selected-focus-points="selectedFocusPoints"
            :park-files="parkFiles"
          />
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { apiService } from '../utils/api'
import { ElMessage } from 'element-plus'
import CollapsibleSearchForm from '../components/CollapsibleSearchForm.vue'
import SearchResults from '../components/SearchResults.vue'

export default {
  name: 'SearchPage',
  components: {
    CollapsibleSearchForm,
    SearchResults,
  },
  setup() {
    // 基础数据
    const regions = ref([])
    const industryOptions = ref([])
    const generalTags = ref([])
    const industryRequirements = ref(null)

    // 状态管理
    const searching = ref(false)
    const hasSearchResults = ref(false)
    const isFormCollapsed = ref(false)
    const requiredResults = reactive({
      sanxianyidan: [],
      industry_policies: [],
      park_policies: []
    })
    const optionalResults = ref([])
    const selectedFocusPoints = ref([])
    const currentFormData = ref(null)
    const parkFiles = ref([])

    // 表单引用
    const searchFormRef = ref(null)

    // 计算属性：提取选中的行业信息（代码和名称）
    const selectedIndustryInfo = computed(() => {
      if (!currentFormData.value || !currentFormData.value.industryId || currentFormData.value.industryId.length === 0) {
        return { code: '', name: '' };
      }
      
      const industryId = currentFormData.value.industryId[currentFormData.value.industryId.length - 1];
      
      // 在行业选项中递归查找对应的行业对象
      const findIndustry = (options, targetId) => {
        for (const option of options) {
          if (option.id === targetId) {
            return { code: option.code || '', name: option.name };
          }
          if (option.children && option.children.length > 0) {
            const found = findIndustry(option.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };
      
      const foundIndustry = findIndustry(industryOptions.value, industryId);
      return foundIndustry || { code: '', name: '' };
    });

    // 初始化数据
    onMounted(async () => {
      await Promise.all([
        loadRegions(),
        loadIndustries(),
        loadGeneralTags()
      ])
    })

    // 加载区域列表
    const loadRegions = async () => {
      try {
        const response = await apiService.getRegions()
        regions.value = response.data
      } catch (error) {
        console.error('加载区域失败:', error)
        ElMessage.error('加载区域失败')
      }
    }

    // 加载行业树形结构
    const loadIndustries = async () => {
      try {
        const response = await apiService.getIndustries()
        industryOptions.value = response.data
      } catch (error) {
        console.error('加载行业失败:', error)
        ElMessage.error('加载行业失败')
      }
    }

    // 加载通用关注点
    const loadGeneralTags = async () => {
      try {
        const response = await apiService.getGeneralFocusPoints({ pageSize: 1000 })
        generalTags.value = response.data.data
      } catch (error) {
        console.error('加载通用关注点失败:', error)
        ElMessage.error('加载通用关注点失败')
      }
    }

    // 加载行业环评要求
    const loadIndustryRequirements = async (industryId) => {
      try {
        const response = await apiService.getIndustryRequirements(industryId)
        industryRequirements.value = response.data
        return response
      } catch (error) {
        console.error('加载行业环评要求失败:', error)
        ElMessage.error('加载行业环评要求失败')
        industryRequirements.value = null
        throw error
      }
    }

    // 执行搜索
    const handleSearch = async (formData) => {
      searching.value = true
      hasSearchResults.value = false
      currentFormData.value = formData

      try {
        const industryId = formData.industryId[formData.industryId.length - 1]
        const searchPromises = [
          searchRequired(formData),
          loadIndustryRequirements(industryId)
        ]
        
        if (formData.industryKeywords.length > 0 || formData.generalTags.length > 0) {
          searchPromises.push(searchOptional(formData))
        } else {
          optionalResults.value = []
        }
        
        await Promise.all(searchPromises)
        hasSearchResults.value = true
        isFormCollapsed.value = true
      } catch (error) {
        console.error('查询失败:', error)
        ElMessage.error('查询失败，请重试')
        isFormCollapsed.value = false
      } finally {
        searching.value = false
      }
    }

    // 必选项查询
    const searchRequired = async (formData) => {
      const params = {
        region_id: formData.regionId,
        in_park: formData.inPark,
        industry_id: formData.industryId[formData.industryId.length - 1]
      }

      if (formData.inPark) {
        params.park_id = formData.parkOrUnitId
      } else {
        params.control_unit_id = formData.parkOrUnitId
      }

      const response = await apiService.searchRequired(params)
      Object.assign(requiredResults, response.data)

      // 如果是园区搜索，获取园区文件
      if (formData.inPark && formData.parkOrUnitId) {
        try {
          const parkResponse = await apiService.getParkById(formData.parkOrUnitId)
          if (parkResponse.data && parkResponse.data.files) {
            parkFiles.value = JSON.parse(parkResponse.data.files)
          } else {
            parkFiles.value = []
          }
        } catch (error) {
          console.error('获取园区文件失败:', error)
          parkFiles.value = []
        }
      } else {
        parkFiles.value = []
      }
    }

    // 可选项查询
    const searchOptional = async (formData) => {
      const params = {
        industry_keywords: formData.industryKeywords,
        general_tags: formData.generalTags,
        region_id: formData.regionId,
        park_id: formData.inPark ? formData.parkOrUnitId : null,
        industry_id: formData.industryId[formData.industryId.length - 1]
      }

      const response = await apiService.searchOptional(params)
      optionalResults.value = response.data
      selectedFocusPoints.value = response.data // 关注点信息就在返回的数据中
    }

    // 可选项变化处理
    const handleOptionalChange = () => {
      if (hasSearchResults.value && searchFormRef.value) {
        const formData = searchFormRef.value.getFormData()
        searchOptional(formData)
      }
    }

    // 重置表单
    const handleReset = () => {
      industryRequirements.value = null
      hasSearchResults.value = false
      currentFormData.value = null
      Object.assign(requiredResults, {
        sanxianyidan: [],
        industry_policies: [],
        park_policies: []
      })
      optionalResults.value = []
      isFormCollapsed.value = false
    }

    return {
      // 数据
      regions,
      industryOptions,
      generalTags,
      industryRequirements,
      searching,
      hasSearchResults,
      requiredResults,
      optionalResults,
      selectedFocusPoints,
      parkFiles,
      searchFormRef,
      selectedIndustryInfo,
      isFormCollapsed,

      // 方法
      handleSearch,
      handleReset,
      handleOptionalChange
    }
  }
}
</script>

<style scoped>
.search-page-container {
  position: relative;
  overflow-x: hidden;
}

.header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 450px;
    background-image: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    z-index: 0;
    clip-path: polygon(0 0, 100% 0, 100% 80%, 0 100%);
}

.search-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.page-header {
  text-align: center;
  padding: 80px 0 64px;
}

.logo-title {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
}

.logo-icon-wrapper {
    color: white;
}

.page-title {
  font-size: 3.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 15px rgba(0,0,0,0.2);
}

.page-subtitle {
  font-size: 1.1rem;
  color: #5a6a7a;
  margin: 0;
}

.main-content {
  padding-bottom: 64px;
}

@media (max-width: 768px) {
  .search-page {
    padding: 0 16px;
  }
  .page-header {
    padding: 60px 0 48px;
  }
  .header-bg {
    clip-path: polygon(0 0, 100% 0, 100% 88%, 0 100%);
  }
  .logo-icon-wrapper svg {
      width: 40px;
      height: 40px;
  }
  .page-title {
    font-size: 2.8rem;
  }
  .logo-title {
      gap: 16px;
  }
  .page-subtitle {
    font-size: 1rem;
  }
}
</style> 