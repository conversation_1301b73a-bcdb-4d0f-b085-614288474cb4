<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon control-units">
              <el-icon><Location /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.controlUnits }}</div>
              <div class="stat-label">管控单元</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon industries">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.industries }}</div>
              <div class="stat-label">分类管理名录</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon general-focus">
              <el-icon><PriceTag /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.generalFocusPoints }}</div>
              <div class="stat-label">通用关注点</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon industry-focus">
              <el-icon><Tickets /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.industryFocusPoints }}</div>
              <div class="stat-label">行业关注点</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二行统计 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon parks">
              <el-icon><MapLocation /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.parks }}</div>
              <div class="stat-label">开发区</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon classifications">
              <el-icon><Menu /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.classifications }}</div>
              <div class="stat-label">国民经济行业分类</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon regions">
              <el-icon><Location /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.regions }}</div>
              <div class="stat-label">区域</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-item">
            <div class="stat-icon categories">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.categories }}</div>
              <div class="stat-label">园区要求分类</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 功能区域 -->
    <el-row :gutter="20" style="margin-top: 30px;">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Star /></el-icon>
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <div class="action-button primary" @click="$router.push('/admin/control-units')">
              <div class="action-icon">
                <el-icon><Location /></el-icon>
              </div>
              <span class="action-text">管控单元管理</span>
            </div>

            <div class="action-button success" @click="$router.push('/admin/industries')">
              <div class="action-icon">
                <el-icon><OfficeBuilding /></el-icon>
              </div>
              <span class="action-text">分类管理名录</span>
            </div>

            <div class="action-button warning" @click="$router.push('/admin/general-focus-points')">
              <div class="action-icon">
                <el-icon><PriceTag /></el-icon>
              </div>
              <span class="action-text">通用关注点</span>
            </div>

            <div class="action-button info" @click="$router.push('/admin/industry-focus-points')">
              <div class="action-icon">
                <el-icon><Tickets /></el-icon>
              </div>
              <span class="action-text">行业关注点</span>
            </div>

            <div class="action-button secondary" @click="$router.push('/admin/parks')">
              <div class="action-icon">
                <el-icon><MapLocation /></el-icon>
              </div>
              <span class="action-text">开发区管理</span>
            </div>

            <div class="action-button default" @click="$router.push('/admin/classifications')">
              <div class="action-icon">
                <el-icon><Menu /></el-icon>
              </div>
              <span class="action-text">行业分类</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库：</span>
              <span class="info-value">SQLite</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后更新：</span>
              <span class="info-value">{{ lastUpdateTime }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  Location, OfficeBuilding, MapLocation, Star,
  PriceTag, Tickets, Menu, FolderOpened
} from '@element-plus/icons-vue'
import { adminApiService } from '@/utils/api'

const stats = ref({
  controlUnits: 0,
  industries: 0,
  generalFocusPoints: 0,
  industryFocusPoints: 0,
  parks: 0,
  classifications: 0,
  regions: 0,
  categories: 0
})

const lastUpdateTime = ref('')

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里可以调用后端API获取统计数据
    // 暂时使用模拟数据
    stats.value = {
      controlUnits: 87,
      industries: 293,
      generalFocusPoints: 15,
      industryFocusPoints: 25,
      parks: 14,
      classifications: 1456,
      regions: 8,
      categories: 12
    }

    lastUpdateTime.value = new Date().toLocaleString()
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.control-units {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.industries {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.general-focus {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.industry-focus {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-icon.parks {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.classifications {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-icon.regions {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.stat-icon.categories {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.action-button {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-button:hover::before {
  opacity: 0.1;
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
}

.action-text {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

/* 按钮主题色彩 */
.action-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-button.primary .action-text {
  color: white;
}

.action-button.primary .action-icon {
  background: rgba(255, 255, 255, 0.2);
}

.action-button.success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.action-button.success .action-text {
  color: white;
}

.action-button.success .action-icon {
  background: rgba(255, 255, 255, 0.2);
}

.action-button.warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.action-button.warning .action-text {
  color: white;
}

.action-button.warning .action-icon {
  background: rgba(255, 255, 255, 0.2);
}

.action-button.info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.action-button.info .action-text {
  color: white;
}

.action-button.info .action-icon {
  background: rgba(255, 255, 255, 0.2);
}

.action-button.secondary {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.action-button.secondary .action-text {
  color: white;
}

.action-button.secondary .action-icon {
  background: rgba(255, 255, 255, 0.2);
}

.action-button.default {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #303133;
}

.action-button.default .action-icon {
  background: rgba(48, 49, 51, 0.1);
  color: #303133;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.system-info {
  padding: 10px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #909399;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}
</style>
