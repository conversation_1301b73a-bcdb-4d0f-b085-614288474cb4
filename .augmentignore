# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/
env/
ENV/
.venv/
.env
.ENV/

# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
target/
.gradle/
build/
gradle-app.setting
!gradle-wrapper.jar
!gradle-wrapper.properties

# .NET
bin/
obj/
*.user
*.suo
*.cache
*.dll
*.exe
*.pdb
*.userprefs
*.pidb
*.booproj
*.svd
packages/
TestResults/

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
vendor/

# Rust
target/
Cargo.lock

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
log/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Database
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/
.serverless/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Large media files
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv
*.m4v
*.3gp
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma
*.m4a
*.zip
*.rar
*.7z
*.tar
*.gz
*.bz2
*.xz

# Documentation build
docs/_build/
site/
_site/

# Test coverage
coverage/
.nyc_output/
.coverage
htmlcov/
.tox/
.pytest_cache/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Lock files (optional - uncomment if you want to ignore them)
# Cargo.lock
# package-lock.json
# yarn.lock
# composer.lock
# Pipfile.lock