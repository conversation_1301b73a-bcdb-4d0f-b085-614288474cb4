const ControlUnit = require('../models/ControlUnit');
const Industry = require('../models/Industry');
const IndustryClassification = require('../models/IndustryClassification');
const ParkRequirementCategory = require('../models/ParkRequirementCategory');
const Park = require('../models/Park');
const FocusPoint = require('../models/FocusPoint');
const GeneralFocusPoint = require('../models/GeneralFocusPoint');
const IndustryFocusPoint = require('../models/IndustryFocusPoint');
const Region = require('../models/Region');

class AdminController {
  // 三线一单管理
  static async getControlUnits(req, res) {
    try {
      const { page = 1, pageSize = 10, search, region_id, unit_type } = req.query;
      const filters = { search, region_id, unit_type };
      
      const result = await ControlUnit.getPaginated(parseInt(page), parseInt(pageSize), filters);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取管控单元列表失败:', error);
      res.status(500).json({ code: 500, message: '获取管控单元列表失败', error: error.message });
    }
  }

  static async getControlUnit(req, res) {
    try {
      const { id } = req.params;
      const controlUnit = await ControlUnit.getById(id);
      
      if (!controlUnit) {
        return res.status(404).json({ code: 404, message: '管控单元不存在' });
      }
      
      res.json({ code: 200, data: controlUnit });
    } catch (error) {
      console.error('获取管控单元详情失败:', error);
      res.status(500).json({ code: 500, message: '获取管控单元详情失败', error: error.message });
    }
  }

  static async createControlUnit(req, res) {
    try {
      const controlUnit = await ControlUnit.create(req.body);
      res.json({ code: 200, data: controlUnit, message: '创建成功' });
    } catch (error) {
      console.error('创建管控单元失败:', error);
      res.status(500).json({ code: 500, message: '创建管控单元失败', error: error.message });
    }
  }

  static async updateControlUnit(req, res) {
    try {
      const { id } = req.params;
      const controlUnit = await ControlUnit.update(id, req.body);
      res.json({ code: 200, data: controlUnit, message: '更新成功' });
    } catch (error) {
      console.error('更新管控单元失败:', error);
      res.status(500).json({ code: 500, message: '更新管控单元失败', error: error.message });
    }
  }

  static async deleteControlUnit(req, res) {
    try {
      const { id } = req.params;
      const result = await ControlUnit.delete(id);
      
      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '管控单元不存在' });
      }
    } catch (error) {
      console.error('删除管控单元失败:', error);
      res.status(500).json({ code: 500, message: '删除管控单元失败', error: error.message });
    }
  }

  // 分类管理名录管理
  static async getIndustries(req, res) {
    try {
      const { page = 1, pageSize = 10, search, category } = req.query;
      const filters = { search, category };
      
      const result = await Industry.getPaginated(parseInt(page), parseInt(pageSize), filters);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取行业列表失败:', error);
      res.status(500).json({ code: 500, message: '获取行业列表失败', error: error.message });
    }
  }

  static async getIndustryCategories(req, res) {
    try {
      const categories = await Industry.getCategories();
      res.json({ code: 200, data: categories });
    } catch (error) {
      console.error('获取行业分类失败:', error);
      res.status(500).json({ code: 500, message: '获取行业分类失败', error: error.message });
    }
  }

  static async getIndustry(req, res) {
    try {
      const { id } = req.params;
      const industry = await Industry.getById(id);
      
      if (!industry) {
        return res.status(404).json({ code: 404, message: '行业不存在' });
      }
      
      res.json({ code: 200, data: industry });
    } catch (error) {
      console.error('获取行业详情失败:', error);
      res.status(500).json({ code: 500, message: '获取行业详情失败', error: error.message });
    }
  }

  static async createIndustry(req, res) {
    try {
      const industry = await Industry.create(req.body);
      res.json({ code: 200, data: industry, message: '创建成功' });
    } catch (error) {
      console.error('创建行业失败:', error);
      res.status(500).json({ code: 500, message: '创建行业失败', error: error.message });
    }
  }

  static async updateIndustry(req, res) {
    try {
      const { id } = req.params;
      const industry = await Industry.update(id, req.body);
      res.json({ code: 200, data: industry, message: '更新成功' });
    } catch (error) {
      console.error('更新行业失败:', error);
      res.status(500).json({ code: 500, message: '更新行业失败', error: error.message });
    }
  }

  static async deleteIndustry(req, res) {
    try {
      const { id } = req.params;
      const result = await Industry.delete(id);
      
      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '行业不存在' });
      }
    } catch (error) {
      console.error('删除行业失败:', error);
      res.status(500).json({ code: 500, message: '删除行业失败', error: error.message });
    }
  }

  // 国民经济行业分类管理
  static async getClassifications(req, res) {
    try {
      const { page = 1, pageSize = 10, search, level, parent_code } = req.query;
      const filters = { search, level, parent_code };
      
      const result = await IndustryClassification.getPaginated(parseInt(page), parseInt(pageSize), filters);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取行业分类列表失败:', error);
      res.status(500).json({ code: 500, message: '获取行业分类列表失败', error: error.message });
    }
  }

  static async getClassificationTree(req, res) {
    try {
      const { parent_code } = req.query;
      const result = await IndustryClassification.getTreeData(parent_code);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取行业分类树失败:', error);
      res.status(500).json({ code: 500, message: '获取行业分类树失败', error: error.message });
    }
  }

  static async getClassification(req, res) {
    try {
      const { id } = req.params;
      const classification = await IndustryClassification.getById(id);
      
      if (!classification) {
        return res.status(404).json({ code: 404, message: '行业分类不存在' });
      }
      
      res.json({ code: 200, data: classification });
    } catch (error) {
      console.error('获取行业分类详情失败:', error);
      res.status(500).json({ code: 500, message: '获取行业分类详情失败', error: error.message });
    }
  }

  static async createClassification(req, res) {
    try {
      const classification = await IndustryClassification.create(req.body);
      res.json({ code: 200, data: classification, message: '创建成功' });
    } catch (error) {
      console.error('创建行业分类失败:', error);
      res.status(500).json({ code: 500, message: '创建行业分类失败', error: error.message });
    }
  }

  static async updateClassification(req, res) {
    try {
      const { id } = req.params;
      const classification = await IndustryClassification.update(id, req.body);
      res.json({ code: 200, data: classification, message: '更新成功' });
    } catch (error) {
      console.error('更新行业分类失败:', error);
      res.status(500).json({ code: 500, message: '更新行业分类失败', error: error.message });
    }
  }

  static async deleteClassification(req, res) {
    try {
      const { id } = req.params;
      const result = await IndustryClassification.delete(id);
      
      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '行业分类不存在' });
      }
    } catch (error) {
      console.error('删除行业分类失败:', error);
      res.status(500).json({ code: 500, message: '删除行业分类失败', error: error.message });
    }
  }

  // 开发区管理
  static async getParks(req, res) {
    try {
      const { page = 1, pageSize = 10, search } = req.query;
      const result = await Park.getPaginated(parseInt(page), parseInt(pageSize), search);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取园区列表失败:', error);
      res.status(500).json({ code: 500, message: '获取园区列表失败', error: error.message });
    }
  }

  static async getPark(req, res) {
    try {
      const { id } = req.params;
      const park = await Park.getById(id);
      
      if (!park) {
        return res.status(404).json({ code: 404, message: '园区不存在' });
      }
      
      res.json({ code: 200, data: park });
    } catch (error) {
      console.error('获取园区详情失败:', error);
      res.status(500).json({ code: 500, message: '获取园区详情失败', error: error.message });
    }
  }

  static async createPark(req, res) {
    try {
      const park = await Park.create(req.body);
      res.json({ code: 200, data: park, message: '创建成功' });
    } catch (error) {
      console.error('创建园区失败:', error);
      res.status(500).json({ code: 500, message: '创建园区失败', error: error.message });
    }
  }

  static async updatePark(req, res) {
    try {
      const { id } = req.params;
      const park = await Park.update(id, req.body);
      res.json({ code: 200, data: park, message: '更新成功' });
    } catch (error) {
      console.error('更新园区失败:', error);
      res.status(500).json({ code: 500, message: '更新园区失败', error: error.message });
    }
  }

  static async deletePark(req, res) {
    try {
      const { id } = req.params;
      const result = await Park.delete(id);
      
      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '园区不存在' });
      }
    } catch (error) {
      console.error('删除园区失败:', error);
      res.status(500).json({ code: 500, message: '删除园区失败', error: error.message });
    }
  }

  // 园区要求管理
  static async getParkRequirements(req, res) {
    try {
      const { id } = req.params;
      const requirements = await Park.getRequirements(id);
      res.json({ code: 200, data: requirements });
    } catch (error) {
      console.error('获取园区要求失败:', error);
      res.status(500).json({ code: 500, message: '获取园区要求失败', error: error.message });
    }
  }

  static async addParkRequirement(req, res) {
    try {
      const { id } = req.params;
      const requirement = await Park.addRequirement(id, req.body);
      res.json({ code: 200, data: requirement, message: '添加成功' });
    } catch (error) {
      console.error('添加园区要求失败:', error);
      res.status(500).json({ code: 500, message: '添加园区要求失败', error: error.message });
    }
  }

  static async updateParkRequirement(req, res) {
    try {
      const { requirementId } = req.params;
      const requirement = await Park.updateRequirement(requirementId, req.body);
      res.json({ code: 200, data: requirement, message: '更新成功' });
    } catch (error) {
      console.error('更新园区要求失败:', error);
      res.status(500).json({ code: 500, message: '更新园区要求失败', error: error.message });
    }
  }

  static async deleteParkRequirement(req, res) {
    try {
      const { requirementId } = req.params;
      const result = await Park.deleteRequirement(requirementId);
      
      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '要求不存在' });
      }
    } catch (error) {
      console.error('删除园区要求失败:', error);
      res.status(500).json({ code: 500, message: '删除园区要求失败', error: error.message });
    }
  }

  // 关注点管理
  static async getFocusPoints(req, res) {
    try {
      const { page = 1, pageSize = 10, search, category, is_active } = req.query;
      const filters = { search, category, is_active };
      
      const result = await FocusPoint.getPaginated(parseInt(page), parseInt(pageSize), filters);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取关注点列表失败:', error);
      res.status(500).json({ code: 500, message: '获取关注点列表失败', error: error.message });
    }
  }

  static async getFocusPointCategories(req, res) {
    try {
      const categories = await FocusPoint.getCategories();
      res.json({ code: 200, data: categories });
    } catch (error) {
      console.error('获取关注点分类失败:', error);
      res.status(500).json({ code: 500, message: '获取关注点分类失败', error: error.message });
    }
  }

  static async getFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const focusPoint = await FocusPoint.getById(id);
      
      if (!focusPoint) {
        return res.status(404).json({ code: 404, message: '关注点不存在' });
      }
      
      res.json({ code: 200, data: focusPoint });
    } catch (error) {
      console.error('获取关注点详情失败:', error);
      res.status(500).json({ code: 500, message: '获取关注点详情失败', error: error.message });
    }
  }

  static async createFocusPoint(req, res) {
    try {
      const focusPoint = await FocusPoint.create(req.body);
      res.json({ code: 200, data: focusPoint, message: '创建成功' });
    } catch (error) {
      console.error('创建关注点失败:', error);
      res.status(500).json({ code: 500, message: '创建关注点失败', error: error.message });
    }
  }

  static async updateFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const focusPoint = await FocusPoint.update(id, req.body);
      res.json({ code: 200, data: focusPoint, message: '更新成功' });
    } catch (error) {
      console.error('更新关注点失败:', error);
      res.status(500).json({ code: 500, message: '更新关注点失败', error: error.message });
    }
  }

  static async deleteFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const result = await FocusPoint.delete(id);

      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '关注点不存在' });
      }
    } catch (error) {
      console.error('删除关注点失败:', error);
      res.status(500).json({ code: 500, message: '删除关注点失败', error: error.message });
    }
  }

  // 通用关注点管理
  static async getGeneralFocusPoints(req, res) {
    try {
      const { page = 1, pageSize = 10, search, is_active } = req.query;
      const filters = { search, is_active };

      const result = await GeneralFocusPoint.getPaginated(parseInt(page), parseInt(pageSize), filters);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取通用关注点列表失败:', error);
      res.status(500).json({ code: 500, message: '获取通用关注点列表失败', error: error.message });
    }
  }

  static async getGeneralFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const focusPoint = await GeneralFocusPoint.getById(id);

      if (!focusPoint) {
        return res.status(404).json({ code: 404, message: '通用关注点不存在' });
      }

      res.json({ code: 200, data: focusPoint });
    } catch (error) {
      console.error('获取通用关注点详情失败:', error);
      res.status(500).json({ code: 500, message: '获取通用关注点详情失败', error: error.message });
    }
  }

  static async createGeneralFocusPoint(req, res) {
    try {
      const focusPoint = await GeneralFocusPoint.create(req.body);
      res.json({ code: 200, data: focusPoint, message: '创建成功' });
    } catch (error) {
      console.error('创建通用关注点失败:', error);
      res.status(500).json({ code: 500, message: '创建通用关注点失败', error: error.message });
    }
  }

  static async updateGeneralFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const focusPoint = await GeneralFocusPoint.update(id, req.body);
      res.json({ code: 200, data: focusPoint, message: '更新成功' });
    } catch (error) {
      console.error('更新通用关注点失败:', error);
      res.status(500).json({ code: 500, message: '更新通用关注点失败', error: error.message });
    }
  }

  static async deleteGeneralFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const result = await GeneralFocusPoint.delete(id);

      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '通用关注点不存在' });
      }
    } catch (error) {
      console.error('删除通用关注点失败:', error);
      res.status(500).json({ code: 500, message: '删除通用关注点失败', error: error.message });
    }
  }

  // 行业关注点管理
  static async getIndustryFocusPoints(req, res) {
    try {
      const { page = 1, pageSize = 10, search, industry_id, is_active } = req.query;
      const filters = { search, industry_id, is_active };

      const result = await IndustryFocusPoint.getPaginated(parseInt(page), parseInt(pageSize), filters);
      res.json({ code: 200, data: result });
    } catch (error) {
      console.error('获取行业关注点列表失败:', error);
      res.status(500).json({ code: 500, message: '获取行业关注点列表失败', error: error.message });
    }
  }

  static async getIndustryFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const focusPoint = await IndustryFocusPoint.getById(id);

      if (!focusPoint) {
        return res.status(404).json({ code: 404, message: '行业关注点不存在' });
      }

      res.json({ code: 200, data: focusPoint });
    } catch (error) {
      console.error('获取行业关注点详情失败:', error);
      res.status(500).json({ code: 500, message: '获取行业关注点详情失败', error: error.message });
    }
  }

  static async createIndustryFocusPoint(req, res) {
    try {
      const focusPoint = await IndustryFocusPoint.create(req.body);
      res.json({ code: 200, data: focusPoint, message: '创建成功' });
    } catch (error) {
      console.error('创建行业关注点失败:', error);
      res.status(500).json({ code: 500, message: '创建行业关注点失败', error: error.message });
    }
  }

  static async updateIndustryFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const focusPoint = await IndustryFocusPoint.update(id, req.body);
      res.json({ code: 200, data: focusPoint, message: '更新成功' });
    } catch (error) {
      console.error('更新行业关注点失败:', error);
      res.status(500).json({ code: 500, message: '更新行业关注点失败', error: error.message });
    }
  }

  static async deleteIndustryFocusPoint(req, res) {
    try {
      const { id } = req.params;
      const result = await IndustryFocusPoint.delete(id);

      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '行业关注点不存在' });
      }
    } catch (error) {
      console.error('删除行业关注点失败:', error);
      res.status(500).json({ code: 500, message: '删除行业关注点失败', error: error.message });
    }
  }

  static async updateFocusPointSortOrder(req, res) {
    try {
      const { updates } = req.body;
      const result = await FocusPoint.updateSortOrder(updates);
      res.json({ code: 200, data: result, message: '排序更新成功' });
    } catch (error) {
      console.error('更新关注点排序失败:', error);
      res.status(500).json({ code: 500, message: '更新关注点排序失败', error: error.message });
    }
  }

  // 获取区域列表（用于下拉选择）
  static async getRegions(req, res) {
    try {
      const regions = await Region.getAll();
      res.json({ code: 200, data: regions });
    } catch (error) {
      console.error('获取区域列表失败:', error);
      res.status(500).json({ code: 500, message: '获取区域列表失败', error: error.message });
    }
  }

  // ==================== 园区要求分类管理 ====================

  // 获取所有分类
  static async getParkRequirementCategories(req, res) {
    try {
      const categories = await ParkRequirementCategory.getAll();
      res.json({ code: 200, data: categories });
    } catch (error) {
      console.error('获取园区要求分类失败:', error);
      res.status(500).json({ code: 500, message: '获取园区要求分类失败', error: error.message });
    }
  }

  // 创建分类
  static async createParkRequirementCategory(req, res) {
    try {
      const { name, description, sort_order } = req.body;

      if (!name) {
        return res.status(400).json({ code: 400, message: '分类名称不能为空' });
      }

      // 检查名称是否已存在
      const nameExists = await ParkRequirementCategory.checkNameExists(name);
      if (nameExists) {
        return res.status(400).json({ code: 400, message: '分类名称已存在' });
      }

      const category = await ParkRequirementCategory.create({
        name,
        description,
        sort_order: sort_order || 0
      });

      res.json({ code: 200, data: category, message: '创建成功' });
    } catch (error) {
      console.error('创建园区要求分类失败:', error);
      res.status(500).json({ code: 500, message: '创建园区要求分类失败', error: error.message });
    }
  }

  // 更新分类
  static async updateParkRequirementCategory(req, res) {
    try {
      const { id } = req.params;
      const { name, description, sort_order } = req.body;

      if (!name) {
        return res.status(400).json({ code: 400, message: '分类名称不能为空' });
      }

      // 检查名称是否已存在（排除当前记录）
      const nameExists = await ParkRequirementCategory.checkNameExists(name, id);
      if (nameExists) {
        return res.status(400).json({ code: 400, message: '分类名称已存在' });
      }

      const category = await ParkRequirementCategory.update(id, {
        name,
        description,
        sort_order
      });

      res.json({ code: 200, data: category, message: '更新成功' });
    } catch (error) {
      console.error('更新园区要求分类失败:', error);
      res.status(500).json({ code: 500, message: '更新园区要求分类失败', error: error.message });
    }
  }

  // 删除分类
  static async deleteParkRequirementCategory(req, res) {
    try {
      const { id } = req.params;
      const result = await ParkRequirementCategory.delete(id);

      if (result.deleted) {
        res.json({ code: 200, message: '删除成功' });
      } else {
        res.status(404).json({ code: 404, message: '分类不存在' });
      }
    } catch (error) {
      console.error('删除园区要求分类失败:', error);
      if (error.message.includes('正在被使用')) {
        res.status(400).json({ code: 400, message: error.message });
      } else {
        res.status(500).json({ code: 500, message: '删除园区要求分类失败', error: error.message });
      }
    }
  }
}

module.exports = AdminController;
