const { initDatabase, getDatabase } = require('./config/database');

async function checkIndustryData() {
  try {
    await initDatabase();
    const db = getDatabase();
    
    console.log('=== 检查行业数据 ===');
    
    // 1. 查看所有行业
    console.log('\n--- 所有行业 ---');
    const industries = await new Promise((resolve, reject) => {
      db.all('SELECT id, name, code FROM industries ORDER BY id', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    industries.forEach(industry => {
      console.log(`ID: ${industry.id}, 名称: ${industry.name}, 代码: ${industry.code || '无'}`);
    });
    
    // 2. 查看所有行业分类
    console.log('\n--- 所有行业分类 ---');
    const classifications = await new Promise((resolve, reject) => {
      db.all('SELECT id, code, name, parent_id FROM industry_classifications ORDER BY code', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    classifications.forEach(cls => {
      console.log(`ID: ${cls.id}, 代码: ${cls.code}, 名称: ${cls.name}, 父级: ${cls.parent_id || '无'}`);
    });
    
    // 3. 查看行业关注点关联
    console.log('\n--- 行业关注点关联 ---');
    const relations = await new Promise((resolve, reject) => {
      db.all(`
        SELECT ifp.name as point_name, i.name as industry_name, i.id as industry_id
        FROM industry_focus_point_relations r
        JOIN industry_focus_points ifp ON r.industry_focus_point_id = ifp.id
        JOIN industries i ON r.industry_id = i.id
        ORDER BY ifp.name, i.name
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    relations.forEach(rel => {
      console.log(`关注点: ${rel.point_name} -> 行业: ${rel.industry_name} (ID: ${rel.industry_id})`);
    });
    
    // 4. 检查问题行业
    console.log('\n--- 检查问题行业 ---');
    const problemIndustry = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM industries WHERE name LIKE "%农产品基地项目%"', (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    if (problemIndustry) {
      console.log('找到问题行业:', problemIndustry);
      
      // 查找对应的行业分类
      const relatedClassification = await new Promise((resolve, reject) => {
        db.get('SELECT * FROM industry_classifications WHERE name LIKE "%农产品%" OR name LIKE "%农业%"', (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });
      
      if (relatedClassification) {
        console.log('找到相关分类:', relatedClassification);
      } else {
        console.log('未找到相关分类');
      }
    } else {
      console.log('未找到问题行业');
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('检查失败:', error);
    process.exit(1);
  }
}

checkIndustryData();
